#!/usr/bin/env python
"""
Test script for the company search endpoint.
"""

import os
import sys
import django
import json
import requests
import time

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_api_project.settings')
django.setup()

def test_company_search():
    """Test the company search endpoint."""
    print("🏢 Company Search API Test")
    print("=" * 30)
    
    # API endpoint
    url = "http://localhost:8000/api/books/company-search/"
    
    test_cases = [
        {
            "name": "Apple by Name (English)",
            "data": {
                "company_name": "Apple",
                "language": "en"
            },
            "expected_code": "AAPL"
        },
        {
            "name": "Apple by Code (English)",
            "data": {
                "company_name": "AAPL",
                "language": "en"
            },
            "expected_name": "Apple"
        },
        {
            "name": "Google (English)",
            "data": {
                "company_name": "Google",
                "language": "en"
            },
            "expected_code": "GOOGL"
        },
        {
            "name": "Microsoft (Arabic)",
            "data": {
                "company_name": "Microsoft",
                "language": "ar"
            },
            "expected_code": "MSFT"
        }
    ]
    
    total_tests = len(test_cases)
    passed_tests = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🏢 Test {i}/{total_tests}: {test_case['name']}")
        print("-" * 45)
        
        start_time = time.time()
        
        try:
            response = requests.post(url, json=test_case['data'], timeout=30)
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"⏱️  Response Time: {response_time:.1f}s")
            
            if response.status_code == 200:
                data = response.json()
                
                print(f"✅ Status: {response.status_code}")
                print(f"🏢 Name: {data.get('name', 'N/A')}")
                print(f"📈 Code: {data.get('code', 'N/A')}")
                print(f"📧 Email: {data.get('company_email', 'N/A')}")
                print(f"🌐 Website: {data.get('web_url', 'N/A')}")
                print(f"🎨 Logo: {'✅' if data.get('logo') else '❌'}")
                print(f"🌍 Country: {data.get('country_origin', 'N/A')}")
                print(f"👨‍💼 CEO: {data.get('ceo', 'N/A')}")
                print(f"📅 Founded: {data.get('founded', 'N/A')}")
                
                # Check category
                category = data.get('category', {})
                if category:
                    print(f"\n📂 Category:")
                    print(f"   Name: {category.get('name', 'N/A')} {category.get('icon', '')}")
                    print(f"   Wiki: {'✅' if category.get('wikilink') else '❌'}")
                    
                    cat_desc = category.get('description', '')
                    cat_words = len(cat_desc.split()) if cat_desc else 0
                    cat_status = "✅" if 80 <= cat_words <= 120 else "❌"
                    print(f"   Description: {cat_words} words {cat_status}")
                
                # Check stock data
                stock_data = data.get('stock_data', {})
                if stock_data:
                    print(f"\n📊 Stock Data:")
                    print(f"   52W Low: ${stock_data.get('last_52_weeks_low', 0)}")
                    print(f"   52W High: ${stock_data.get('last_52_weeks_high', 0)}")
                    print(f"   Market Cap: {stock_data.get('market_cap', 'N/A')}")
                    print(f"   Yesterday Close: ${stock_data.get('yesterday_close', 0)}")

                # Check yesterday's complete data
                yesterday_data = data.get('yesterday_data', {})
                if yesterday_data and yesterday_data.get('Date'):
                    print(f"\n📈 Yesterday's Data:")
                    print(f"   Date: {yesterday_data.get('Date', 'N/A')[:10]}")
                    print(f"   OHLCV: ${yesterday_data.get('Open', 0):.2f} / ${yesterday_data.get('High', 0):.2f} / ${yesterday_data.get('Low', 0):.2f} / ${yesterday_data.get('Close', 0):.2f} / {yesterday_data.get('Volume', 0):,}")
                
                # Check 7-day data
                last_7_days = data.get('last_7_days_data', [])
                print(f"\n📈 7-Day Data: {len(last_7_days)} days")
                if last_7_days:
                    latest = last_7_days[-1]
                    print(f"   Latest: {latest.get('Date', 'N/A')}")
                    print(f"   Close: ${latest.get('Close', 0)}")
                    print(f"   Volume: {latest.get('Volume', 0):,}")
                
                # Validation
                structure_valid = all([
                    data.get('name'),
                    data.get('code'),
                    data.get('web_url'),
                    category
                ])
                
                has_stock_data = bool(stock_data and stock_data.get('market_cap'))
                has_yesterday_data = bool(yesterday_data and yesterday_data.get('Date'))
                has_7day_data = len(last_7_days) > 0
                
                print(f"\n🎯 Assessment:")
                print(f"   Response Time: {'✅ FAST' if response_time < 20 else '❌ SLOW'}")
                print(f"   Structure: {'✅ COMPLETE' if structure_valid else '❌ INCOMPLETE'}")
                print(f"   Stock Data: {'✅ PRESENT' if has_stock_data else '❌ MISSING'}")
                print(f"   Yesterday Data: {'✅ PRESENT' if has_yesterday_data else '❌ MISSING'}")
                print(f"   7-Day Data: {'✅ PRESENT' if has_7day_data else '❌ MISSING'}")
                print(f"   Logo: {'✅ PRESENT' if data.get('logo') else '❌ MISSING'}")
                
                if (response_time < 20 and structure_valid and
                    (has_stock_data or has_yesterday_data or has_7day_data)):
                    passed_tests += 1
                    print(f"   Overall: ✅ PASSED")
                else:
                    print(f"   Overall: ❌ FAILED")
                
            else:
                print(f"❌ Error: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"Error details: {error_data}")
                except:
                    print(f"Response: {response.text}")
                
        except requests.exceptions.Timeout:
            print("❌ Request timed out (> 30s)")
        except requests.exceptions.RequestException as e:
            print(f"❌ Request error: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
        
        # Add delay between tests
        if i < total_tests:
            print("⏳ Waiting 8s before next test...")
            time.sleep(8)
    
    # Final summary
    print("\n" + "=" * 30)
    print("📋 COMPANY SEARCH TEST SUMMARY")
    print("=" * 30)
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Company search endpoint works perfectly")
        print("🏢 Company information is comprehensive")
        print("📊 Stock data is included")
        print("📈 7-day market data is present")
        print("🌍 Language handling is accurate")
    elif passed_tests > 0:
        print("⚠️  PARTIAL SUCCESS")
        print("🔧 Some tests passed, endpoint is functional")
    else:
        print("❌ ALL TESTS FAILED")
        print("🔧 Endpoint has significant issues")
    
    return passed_tests == total_tests

def test_error_handling():
    """Test error handling for the company search endpoint."""
    print("\n🚨 Error Handling Test")
    print("=" * 25)
    
    url = "http://localhost:8000/api/books/company-search/"
    
    error_cases = [
        {
            "name": "Empty company name",
            "data": {"company_name": "", "language": "en"},
            "expected_status": 400
        },
        {
            "name": "Invalid language",
            "data": {"company_name": "Apple", "language": "fr"},
            "expected_status": 400
        },
        {
            "name": "Missing company name",
            "data": {"language": "en"},
            "expected_status": 400
        }
    ]
    
    for case in error_cases:
        print(f"\n🧪 Testing: {case['name']}")
        try:
            response = requests.post(url, json=case['data'], timeout=10)
            if response.status_code == case['expected_status']:
                print(f"✅ Correct error handling (status {response.status_code})")
            else:
                print(f"❌ Unexpected status: {response.status_code}")
        except Exception as e:
            print(f"❌ Request failed: {e}")

def quick_company_test():
    """Quick test of popular companies."""
    print("\n⚡ Quick Company Test")
    print("=" * 23)
    
    url = "http://localhost:8000/api/books/company-search/"
    
    companies = ["Tesla", "Amazon", "Netflix"]
    
    for company in companies:
        print(f"\n🔍 Testing {company}...")
        try:
            response = requests.post(url, json={
                "company_name": company,
                "language": "en"
            }, timeout=20)
            
            if response.status_code == 200:
                data = response.json()
                code = data.get('code', 'N/A')
                category = data.get('category', {})
                stock_data = data.get('stock_data', {})
                
                print(f"✅ {company} ({code}): {category.get('name', 'N/A')} {category.get('icon', '')}")
                print(f"   Market Cap: {stock_data.get('market_cap', 'N/A')}")
                print(f"   Logo: {'✅' if data.get('logo') else '❌'}")
            else:
                print(f"❌ {company} failed: {response.status_code}")
        except Exception as e:
            print(f"❌ {company} error: {e}")
        
        time.sleep(3)  # Short delay

if __name__ == "__main__":
    print("🏢 COMPANY SEARCH ENDPOINT TEST SUITE")
    print("=" * 45)
    
    # Quick test first
    quick_company_test()
    
    # Main functionality test
    main_passed = test_company_search()
    
    # Error handling test
    test_error_handling()
    
    print(f"\n🏁 FINAL RESULT")
    print("=" * 20)
    if main_passed:
        print("✅ COMPANY SEARCH ENDPOINT SUCCESS!")
        print("🏢 Comprehensive company information")
        print("📊 Stock data with 52-week ranges")
        print("📈 7-day market data included")
        print("🎨 Company logos and branding")
        print("🌍 Full language support (Arabic/English)")
        print("🚫 No database operations")
    else:
        print("⚠️  ENDPOINT NEEDS IMPROVEMENT")
        print("🔧 Check the issues identified above")
    
    sys.exit(0 if main_passed else 1)
