#!/usr/bin/env python
"""
Ultra-fast test script for the optimized AI Book Search API.
Tests speed and validates 50-70 word descriptions.
"""

import os
import sys
import django
import json
import requests
import time

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_api_project.settings')
django.setup()

def count_words(text):
    """Count words in a text string."""
    if not text:
        return 0
    return len(text.split())

def is_arabic_text(text):
    """Check if text contains Arabic characters."""
    if not text:
        return False
    arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
    return arabic_chars > len(text) * 0.3

def test_ultra_fast_api():
    """Test API with focus on speed and description quality."""
    print("⚡ Ultra-Fast API Test")
    print("=" * 40)
    
    # API endpoint
    url = "http://localhost:8000/api/books/ai-search/"
    
    # Quick test cases
    test_cases = [
        {
            "name": "English Speed Test",
            "data": {
                "book_name": "Pride and Prejudice",
                "language": "en",
                "max_results": 2
            },
            "target_time": 15,
            "expected_lang": "en"
        },
        {
            "name": "Arabic Speed Test", 
            "data": {
                "book_name": "ألف ليلة وليلة",
                "language": "ar",
                "max_results": 2
            },
            "target_time": 18,
            "expected_lang": "ar"
        }
    ]
    
    total_tests = len(test_cases)
    passed_tests = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🚀 Test {i}/{total_tests}: {test_case['name']}")
        print("-" * 30)
        
        # Measure response time
        start_time = time.time()
        
        try:
            response = requests.post(url, json=test_case['data'], timeout=30)
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"⏱️  Response Time: {response_time:.1f}s (Target: <{test_case['target_time']}s)")
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                print(f"📊 Results: {len(results)} found")
                
                speed_ok = response_time < test_case['target_time']
                descriptions_ok = True
                language_ok = True
                
                # Check each result
                for j, result in enumerate(results, 1):
                    print(f"\n📚 Result {j}:")
                    
                    # Check book summary
                    book_summary = result.get('ai_book_summary', '')
                    if book_summary:
                        summary_words = count_words(book_summary)
                        is_correct_lang = (
                            is_arabic_text(book_summary) if test_case['expected_lang'] == 'ar' 
                            else not is_arabic_text(book_summary)
                        )
                        summary_ok = 80 <= summary_words <= 120
                        print(f"   📄 Summary: {summary_words} words {'✅' if summary_ok else '❌'} | Lang: {'✅' if is_correct_lang else '❌'}")
                        
                        if not summary_ok or not is_correct_lang:
                            descriptions_ok = False
                            if not is_correct_lang:
                                language_ok = False
                    
                    # Check categories
                    categories = result.get('structured_categories', [])
                    print(f"   📂 Categories: {len(categories)}")
                    
                    for k, cat in enumerate(categories, 1):
                        cat_desc = cat.get('description', '')
                        cat_name = cat.get('name', '')
                        
                        desc_words = count_words(cat_desc)
                        desc_ok = 50 <= desc_words <= 70
                        
                        name_correct = (
                            is_arabic_text(cat_name) if test_case['expected_lang'] == 'ar' 
                            else not is_arabic_text(cat_name)
                        )
                        desc_correct = (
                            is_arabic_text(cat_desc) if test_case['expected_lang'] == 'ar' 
                            else not is_arabic_text(cat_desc)
                        )
                        
                        print(f"     {k}. {cat_name} - {desc_words} words {'✅' if desc_ok else '❌'} | Lang: {'✅' if name_correct and desc_correct else '❌'}")
                        
                        if not desc_ok:
                            descriptions_ok = False
                        if not (name_correct and desc_correct):
                            language_ok = False
                    
                    # Check author
                    author = result.get('structured_author', {})
                    if author:
                        author_desc = author.get('description', '')
                        author_prof = author.get('profession', '')
                        
                        desc_words = count_words(author_desc)
                        desc_ok = 50 <= desc_words <= 70
                        
                        desc_correct = (
                            is_arabic_text(author_desc) if test_case['expected_lang'] == 'ar' 
                            else not is_arabic_text(author_desc)
                        )
                        prof_correct = (
                            is_arabic_text(author_prof) if test_case['expected_lang'] == 'ar' 
                            else not is_arabic_text(author_prof)
                        )
                        
                        print(f"   👤 Author: {desc_words} words {'✅' if desc_ok else '❌'} | Lang: {'✅' if desc_correct and prof_correct else '❌'}")
                        
                        if not desc_ok:
                            descriptions_ok = False
                        if not (desc_correct and prof_correct):
                            language_ok = False
                
                # Overall assessment
                print(f"\n🎯 Assessment:")
                print(f"   Speed: {'✅ FAST' if speed_ok else '❌ SLOW'} ({response_time:.1f}s)")
                print(f"   Descriptions: {'✅ CORRECT' if descriptions_ok else '❌ WRONG LENGTH'}")
                print(f"   Language: {'✅ CORRECT' if language_ok else '❌ WRONG LANGUAGE'}")
                
                if speed_ok and descriptions_ok and language_ok:
                    passed_tests += 1
                    print(f"   Overall: ✅ PASSED")
                else:
                    print(f"   Overall: ❌ FAILED")
                    if not speed_ok:
                        print(f"     - Too slow: {response_time:.1f}s > {test_case['target_time']}s")
                    if not descriptions_ok:
                        print(f"     - Wrong word counts (need 50-70 for descriptions, 80-120 for summary)")
                    if not language_ok:
                        print(f"     - Wrong language content")
                
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        except requests.exceptions.Timeout:
            print("❌ Request timed out (> 30s)")
        except requests.exceptions.RequestException as e:
            print(f"❌ Request error: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
    
    # Final summary
    print("\n" + "=" * 40)
    print("📋 ULTRA-FAST TEST SUMMARY")
    print("=" * 40)
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print("⚡ API is ultra-fast and accurate!")
        print("📝 Descriptions are correct length")
        print("🌍 Language handling is perfect")
    elif passed_tests > 0:
        print("⚠️  PARTIAL SUCCESS")
        print("🔧 Some issues need fixing")
    else:
        print("❌ MAJOR ISSUES")
        print("🔧 Significant optimization needed")
    
    return passed_tests == total_tests

def test_direct_llm_speed():
    """Test direct LLM speed."""
    print("\n🤖 Direct LLM Speed Test")
    print("=" * 30)
    
    from books.services.llm_service import LLMService
    
    llm_service = LLMService()
    
    # Test English
    print("Testing English LLM...")
    start_time = time.time()
    
    en_info = llm_service.get_combined_structured_info(
        ["Fiction", "Romance"], "Jane Austen", "Pride and Prejudice", "en"
    )
    
    en_time = time.time() - start_time
    print(f"⏱️  English: {en_time:.1f}s")
    
    # Test Arabic
    print("Testing Arabic LLM...")
    start_time = time.time()
    
    ar_info = llm_service.get_combined_structured_info(
        ["أدب", "رومانسية"], "جين أوستن", "كبرياء وتحامل", "ar"
    )
    
    ar_time = time.time() - start_time
    print(f"⏱️  Arabic: {ar_time:.1f}s")
    
    # Check word counts
    en_cat_words = count_words(en_info.get('categories', [{}])[0].get('description', '')) if en_info.get('categories') else 0
    ar_cat_words = count_words(ar_info.get('categories', [{}])[0].get('description', '')) if ar_info.get('categories') else 0
    
    print(f"📊 Results:")
    print(f"   English category desc: {en_cat_words} words {'✅' if 50 <= en_cat_words <= 70 else '❌'}")
    print(f"   Arabic category desc: {ar_cat_words} words {'✅' if 50 <= ar_cat_words <= 70 else '❌'}")
    print(f"   Speed: {'✅ FAST' if max(en_time, ar_time) < 5 else '❌ SLOW'}")
    
    return max(en_time, ar_time) < 5 and 50 <= en_cat_words <= 70 and 50 <= ar_cat_words <= 70

if __name__ == "__main__":
    print("⚡ ULTRA-FAST TEST SUITE")
    print("=" * 50)
    
    # Test direct LLM first
    llm_passed = test_direct_llm_speed()
    
    # Test full API
    api_passed = test_ultra_fast_api()
    
    print(f"\n🏁 FINAL RESULT")
    print("=" * 25)
    if llm_passed and api_passed:
        print("✅ ULTRA-FAST OPTIMIZATION SUCCESS!")
        print("⚡ API responds in < 18 seconds")
        print("📝 All descriptions are 50-70 words")
        print("🌍 Language handling is perfect")
    else:
        print("⚠️  OPTIMIZATION NEEDED")
        if not llm_passed:
            print("🔧 LLM needs speed/description fixes")
        if not api_passed:
            print("🔧 API needs speed/description fixes")
    
    sys.exit(0 if (llm_passed and api_passed) else 1)
