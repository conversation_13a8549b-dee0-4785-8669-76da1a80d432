#!/usr/bin/env python
"""
Quick test for the category search endpoint.
"""

import requests
import json

def test_entertainment_category():
    """Test Entertainment category search."""
    print("🎬 Testing Entertainment Category Search")
    print("=" * 40)
    
    url = "http://localhost:8000/api/books/category-search/"
    
    test_data = {
        "category_name": "Entertainment",
        "language": "en"
    }
    
    print(f"URL: {url}")
    print(f"Data: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data, timeout=20)
        
        print(f"\nResponse Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Success!")
            print(f"\n🏷️ Category Information:")
            print(f"   Name: {data.get('name', 'N/A')}")
            print(f"   Icon: {data.get('icon', 'N/A')}")
            print(f"   Wikipedia: {data.get('wikilink', 'N/A')}")
            
            # Description
            description = data.get('description', '')
            desc_words = len(description.split()) if description else 0
            desc_status = "✅" if 120 <= desc_words <= 180 else "❌"
            print(f"\n📝 Description ({desc_words} words {desc_status}):")
            print(f"   {description[:200]}...")
            
            # Subcategories
            subcategories = data.get('subcategories', [])
            print(f"\n📊 Subcategories ({len(subcategories)}):")
            for subcat in subcategories:
                print(f"   • {subcat}")
            
            # Related fields
            related_fields = data.get('related_fields', [])
            print(f"\n🔗 Related Fields ({len(related_fields)}):")
            for field in related_fields:
                print(f"   • {field}")
            
            # Notable companies
            companies = data.get('notable_companies', [])
            print(f"\n🏢 Notable Companies ({len(companies)}):")
            for company in companies:
                print(f"   • {company}")
            
            # Industry size
            industry_size = data.get('industry_size', '')
            print(f"\n💼 Industry Size:")
            print(f"   {industry_size}")
            
            # Performance
            search_time = data.get('search_time', 0)
            print(f"\n⏱️  Search Time: {search_time:.1f} seconds")
            
            # Validation
            validations = {
                "Has Name": bool(data.get('name')),
                "Has Icon": bool(data.get('icon')),
                "Has Description": bool(description),
                "Description Length": 120 <= desc_words <= 180,
                "Has Subcategories": len(subcategories) > 0,
                "Has Companies": len(companies) > 0,
                "Has Wikipedia": bool(data.get('wikilink')),
                "Has Industry Info": bool(industry_size)
            }
            
            print(f"\n🎯 Validation:")
            for check, result in validations.items():
                print(f"   {check}: {'✅' if result else '❌'}")
            
            all_good = all(validations.values())
            print(f"\n   Overall: {'✅ EXCELLENT' if all_good else '⚠️ NEEDS WORK'}")
            
            return all_good
            
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_technology_category():
    """Test Technology category search."""
    print("\n💻 Testing Technology Category")
    print("=" * 32)
    
    url = "http://localhost:8000/api/books/category-search/"
    
    test_data = {
        "category_name": "Technology",
        "language": "en"
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=20)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Success!")
            print(f"Name: {data.get('name', 'N/A')}")
            print(f"Icon: {data.get('icon', 'N/A')}")
            
            subcategories = data.get('subcategories', [])
            companies = data.get('notable_companies', [])
            
            print(f"Subcategories: {', '.join(subcategories[:3])}")
            print(f"Companies: {', '.join(companies[:3])}")
            
            # Check if we have good data
            has_good_data = (
                len(subcategories) >= 3 and
                len(companies) >= 3 and
                data.get('icon') == '💻'
            )
            
            print(f"Quality: {'✅ GOOD' if has_good_data else '❌ POOR'}")
            
            return has_good_data
            
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_arabic_category():
    """Test Arabic category search."""
    print("\n🌍 Testing Arabic Category")
    print("=" * 27)
    
    url = "http://localhost:8000/api/books/category-search/"
    
    test_data = {
        "category_name": "التعليم",
        "language": "ar"
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=20)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Success!")
            print(f"Name: {data.get('name', 'N/A')}")
            print(f"Icon: {data.get('icon', 'N/A')}")
            
            description = data.get('description', '')
            subcategories = data.get('subcategories', [])
            
            # Check Arabic content
            def has_arabic(text):
                if not text:
                    return False
                arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
                return arabic_chars > len(text) * 0.3
            
            desc_arabic = has_arabic(description)
            subcat_arabic = any(has_arabic(sub) for sub in subcategories)
            
            print(f"Description Arabic: {'✅' if desc_arabic else '❌'}")
            print(f"Subcategories Arabic: {'✅' if subcat_arabic else '❌'}")
            print(f"Description preview: {description[:100]}...")
            
            if subcategories:
                print(f"Subcategories: {', '.join(subcategories[:3])}")
            
            success = desc_arabic and (not subcategories or subcat_arabic)
            print(f"Arabic Quality: {'✅ GOOD' if success else '❌ POOR'}")
            
            return success
            
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_multiple_categories():
    """Test multiple categories quickly."""
    print("\n📊 Testing Multiple Categories")
    print("=" * 31)
    
    url = "http://localhost:8000/api/books/category-search/"
    
    categories = ["Healthcare", "Finance", "Sports"]
    
    results = []
    
    for category in categories:
        print(f"\n🔍 Testing {category}...")
        try:
            response = requests.post(url, json={
                "category_name": category,
                "language": "en"
            }, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                icon = data.get('icon', '')
                subcats = len(data.get('subcategories', []))
                companies = len(data.get('notable_companies', []))
                
                print(f"✅ Success: {icon}")
                print(f"   Data: {subcats} subcategories, {companies} companies")
                
                success = subcats > 0 and companies > 0 and icon
                results.append(success)
            else:
                print(f"❌ Failed: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append(False)
    
    passed = sum(results)
    total = len(results)
    print(f"\n📊 Multiple Categories: {passed}/{total} passed")
    
    return passed >= total // 2

if __name__ == "__main__":
    print("🏷️ QUICK CATEGORY SEARCH TESTS")
    print("=" * 40)
    
    # Test Entertainment (detailed)
    entertainment_ok = test_entertainment_category()
    
    # Test Technology
    technology_ok = test_technology_category()
    
    # Test Arabic
    arabic_ok = test_arabic_category()
    
    # Test multiple categories
    multiple_ok = test_multiple_categories()
    
    print(f"\n🏁 QUICK TEST RESULTS")
    print("=" * 25)
    print(f"Entertainment: {'✅ PASSED' if entertainment_ok else '❌ FAILED'}")
    print(f"Technology: {'✅ PASSED' if technology_ok else '❌ FAILED'}")
    print(f"Arabic: {'✅ PASSED' if arabic_ok else '❌ FAILED'}")
    print(f"Multiple: {'✅ PASSED' if multiple_ok else '❌ FAILED'}")
    
    if entertainment_ok and technology_ok and arabic_ok and multiple_ok:
        print("\n🎉 CATEGORY SEARCH SUCCESS!")
        print("✅ Comprehensive category information")
        print("✅ Proper description word counts")
        print("✅ Rich subcategories and companies")
        print("✅ Industry size information")
        print("✅ Both English and Arabic work")
    elif entertainment_ok or technology_ok:
        print("\n⚠️ PARTIAL SUCCESS")
        print("🔧 Some features working")
    else:
        print("\n❌ CATEGORY SEARCH NEEDS WORK")
        print("🔧 Check endpoint implementation")
