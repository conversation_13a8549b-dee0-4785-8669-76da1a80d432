#!/usr/bin/env python
"""
Test the accuracy fixes for company search.
"""

import requests
import json
import time

def test_accuracy_fixes():
    """Test the accuracy fixes."""
    
    print("=== Testing Company Search Accuracy Fixes ===")
    
    # Test cases
    test_cases = [
        {
            "name": "Microsoft",
            "expected": {
                "code": "MSFT",
                "country": "United States",
                "market_cap_check": lambda x: "T" in x and float(x.replace("T", "")) > 2.0,
                "stock_price_range": (400, 600)
            }
        },
        {
            "name": "Msaari", 
            "expected": {
                "country": "United Arab Emirates",
                "headquarters_check": lambda x: "Dubai" in x or "United Arab Emirates" in x,
                "code_check": lambda x: x == "" or x is None  # Should be empty for private company
            }
        }
    ]
    
    url = "http://localhost:8000/api/books/company-search/"
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: {test_case['name']}")
        print("="*50)
        
        try:
            response = requests.post(
                url, 
                json={"company_name": test_case['name'], "language": "en"}, 
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                
                print(f"✓ Company: {data.get('name', 'N/A')}")
                print(f"✓ Stock Code: {data.get('code', 'N/A')}")
                print(f"✓ Country: {data.get('country_origin', 'N/A')}")
                print(f"✓ Headquarters: {data.get('headquarters', 'N/A')}")
                print(f"✓ CEO: {data.get('ceo', 'N/A')}")
                
                if 'market_cap' in data:
                    print(f"✓ Market Cap: {data['market_cap']}")
                    
                if 'yesterday_close' in data:
                    print(f"✓ Stock Price: ${data['yesterday_close']}")
                
                # Verify expected values
                expected = test_case['expected']
                
                # Check country
                if 'country' in expected:
                    if expected['country'] in data.get('country_origin', ''):
                        print(f"✅ SUCCESS: Country is correct ({data.get('country_origin')})")
                    else:
                        print(f"❌ ERROR: Country is {data.get('country_origin')}, expected {expected['country']}")
                
                # Check stock code
                if 'code' in expected:
                    if data.get('code') == expected['code']:
                        print(f"✅ SUCCESS: Stock code is correct ({expected['code']})")
                    else:
                        print(f"❌ ERROR: Stock code is {data.get('code')}, expected {expected['code']}")
                
                # Check market cap
                if 'market_cap_check' in expected and 'market_cap' in data:
                    if expected['market_cap_check'](data['market_cap']):
                        print(f"✅ SUCCESS: Market cap looks reasonable ({data['market_cap']})")
                    else:
                        print(f"❌ ERROR: Market cap seems wrong ({data['market_cap']})")
                
                # Check stock price range
                if 'stock_price_range' in expected and 'yesterday_close' in data:
                    price = data['yesterday_close']
                    min_price, max_price = expected['stock_price_range']
                    if min_price <= price <= max_price:
                        print(f"✅ SUCCESS: Stock price is in expected range (${price})")
                    else:
                        print(f"❌ ERROR: Stock price ${price} is outside expected range ${min_price}-${max_price}")
                
                # Check headquarters
                if 'headquarters_check' in expected:
                    headquarters = data.get('headquarters', '')
                    if expected['headquarters_check'](headquarters):
                        print(f"✅ SUCCESS: Headquarters looks correct ({headquarters})")
                    else:
                        print(f"❌ ERROR: Headquarters seems wrong ({headquarters})")
                
                # Check code for private companies
                if 'code_check' in expected:
                    code = data.get('code', '')
                    if expected['code_check'](code):
                        print(f"✅ SUCCESS: Stock code correctly empty for private company")
                    else:
                        print(f"❌ ERROR: Private company should not have stock code ({code})")
                        
            else:
                print(f"❌ ERROR: HTTP {response.status_code}")
                print(response.text[:200])
                
        except Exception as e:
            print(f"❌ EXCEPTION: {e}")
        
        time.sleep(2)  # Rate limiting
    
    print(f"\n{'='*60}")
    print("ACCURACY FIXES IMPLEMENTED:")
    print("✅ Enhanced LLM prompts for better accuracy")
    print("✅ Company verification function with known corrections")
    print("✅ Better market cap calculation with fallbacks")
    print("✅ Strict country origin validation")
    print("✅ Private company detection (no stock data)")
    print("✅ Real-time stock data from Yahoo Finance")


if __name__ == "__main__":
    test_accuracy_fixes()
