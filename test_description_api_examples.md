# Book Description Analysis API - Test Examples

## 🚀 Quick Test Commands

### English Description Test
```bash
curl -X POST http://localhost:8000/api/books/analyze-description/ \
  -H "Content-Type: application/json" \
  -d '{
    "description": "A gripping tale of love, betrayal, and redemption set in Victorian England. The story follows <PERSON>, a young woman who must navigate the complex social hierarchies of her time while pursuing her dreams of independence. Through her journey, she encounters mysterious characters, faces moral dilemmas, and discovers the true meaning of courage and sacrifice.",
    "language": "en"
  }'
```

### Arabic Description Test
```bash
curl -X POST http://localhost:8000/api/books/analyze-description/ \
  -H "Content-Type: application/json" \
  -d '{
    "description": "رواية تحكي قصة شاب عربي يسافر عبر الصحراء بحثاً عن كنز مفقود. خلال رحلته يلتقي بشخصيات مختلفة تعلمه دروساً مهمة عن الحياة والحب والصداقة. تستكشف الرواية موضوعات الهوية والانتماء والبحث عن المعنى في الحياة.",
    "language": "ar"
  }'
```

## 📊 Expected Response Format

### English Response
```json
{
    "categories": [
        {
            "name": "Fiction",
            "icon": "📖",
            "wikilink": "https://en.wikipedia.org/wiki/Fiction",
            "description": "Fiction is a literary genre that presents imaginary characters and events created from the author's imagination rather than factual accounts. It encompasses novels, short stories, and novellas that explore human nature, social issues, and philosophical questions through creative narrative storytelling techniques. Popular subgenres include romance, mystery, science fiction, fantasy, and historical fiction, each offering unique perspectives on human experience and creativity, making it one of the most diverse and engaging forms of literature."
        },
        {
            "name": "Romance",
            "icon": "💕",
            "wikilink": "https://en.wikipedia.org/wiki/Romance_novel",
            "description": "Romance is a literary genre that focuses on romantic relationships and emotional connections between characters. These stories typically feature themes of love, passion, courtship, and personal growth through romantic experiences. Romance novels often include elements of drama, conflict, and resolution that test the strength of relationships. The genre spans various subgenres including historical romance, contemporary romance, and paranormal romance, appealing to readers who enjoy emotional storytelling and character development centered around love and relationships."
        }
    ],
    "analysis_summary": "The description indicates a Victorian-era fiction novel with strong romantic elements, focusing on themes of love, social class, and personal growth.",
    "input_description": "A gripping tale of love, betrayal...",
    "language": "en",
    "total_categories": 2
}
```

### Arabic Response
```json
{
    "categories": [
        {
            "name": "رواية عربية",
            "icon": "📖",
            "wikilink": "https://ar.wikipedia.org/wiki/رواية_عربية",
            "description": "الرواية العربية هي شكل من أشكال الأدب العربي الحديث الذي يحكي قصصاً خيالية أو واقعية من خلال شخصيات وأحداث متنوعة. تتناول الروايات العربية موضوعات مختلفة مثل الهوية والتراث والحداثة والقضايا الاجتماعية والسياسية. تطورت الرواية العربية منذ القرن التاسع عشر وأصبحت وسيلة مهمة للتعبير عن الثقافة العربية والتجارب الإنسانية في المجتمعات العربية المعاصرة."
        },
        {
            "name": "أدب الرحلات",
            "icon": "🗺️",
            "wikilink": "https://ar.wikipedia.org/wiki/أدب_الرحلات",
            "description": "أدب الرحلات هو نوع أدبي يركز على وصف الرحلات والاستكشاف والمغامرات في أماكن مختلفة. يتضمن هذا النوع من الأدب وصف الأماكن والثقافات والتجارب الشخصية للرحالة. يساهم أدب الرحلات في توسيع آفاق القراء وتعريفهم بثقافات وحضارات مختلفة. كما يعكس هذا النوع من الأدب روح المغامرة والاستطلاع والبحث عن المعرفة والتجارب الجديدة في العالم."
        }
    ],
    "analysis_summary": "الوصف يشير إلى رواية عربية تتضمن عناصر أدب الرحلات والمغامرة مع التركيز على النمو الشخصي والبحث عن المعنى.",
    "input_description": "رواية تحكي قصة شاب عربي...",
    "language": "ar",
    "total_categories": 2
}
```

## 🧪 Test Scripts

### Run Comprehensive Tests
```bash
# Test both languages
python test_both_languages_description.py

# Test Arabic specifically
python test_arabic_description_analysis.py

# Test full description analysis
python test_description_analysis.py
```

## ✅ Validation Checklist

### For English Responses:
- [ ] All category names in English
- [ ] All descriptions in English (exactly 100 words)
- [ ] English Wikipedia links (en.wikipedia.org)
- [ ] Analysis summary in English
- [ ] Appropriate emojis for categories
- [ ] Response time < 15 seconds

### For Arabic Responses:
- [ ] All category names in Arabic
- [ ] All descriptions in Arabic (exactly 100 words)
- [ ] Arabic Wikipedia links (ar.wikipedia.org)
- [ ] Analysis summary in Arabic
- [ ] Appropriate emojis for categories
- [ ] Response time < 15 seconds

## 🚨 Error Cases

### Invalid Requests
```bash
# Empty description
curl -X POST http://localhost:8000/api/books/analyze-description/ \
  -H "Content-Type: application/json" \
  -d '{"description": "", "language": "en"}'

# Short description
curl -X POST http://localhost:8000/api/books/analyze-description/ \
  -H "Content-Type: application/json" \
  -d '{"description": "Too short", "language": "en"}'

# Invalid language
curl -X POST http://localhost:8000/api/books/analyze-description/ \
  -H "Content-Type: application/json" \
  -d '{"description": "A valid description", "language": "fr"}'
```

### Expected Error Responses
```json
{
    "error": "Description is required"
}
```

```json
{
    "error": "Description must be at least 20 characters long"
}
```

```json
{
    "error": "Language must be \"en\" or \"ar\""
}
```

## 📈 Performance Expectations

- **Response Time**: < 15 seconds for most descriptions
- **Category Count**: 1-3 categories per description
- **Word Count**: Exactly 100 words per category description
- **Language Accuracy**: 100% language-specific content
- **Structure**: Complete category objects with all fields

## 🔧 Troubleshooting

### Common Issues:
1. **Slow Response**: Check Groq API rate limits
2. **Wrong Language**: Verify language parameter
3. **Short Descriptions**: Ensure minimum 20 characters
4. **Missing Categories**: Check if description is clear enough

### Solutions:
1. Add delays between requests
2. Use correct language codes ("en" or "ar")
3. Provide detailed book descriptions
4. Include genre/theme keywords in description
