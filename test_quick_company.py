#!/usr/bin/env python
"""
Quick test for the company search endpoint.
"""

import requests
import json

def test_apple_company():
    """Test Apple company search."""
    print("🍎 Testing Apple Company Search")
    print("=" * 32)
    
    url = "http://localhost:8000/api/books/company-search/"
    
    test_data = {
        "company_name": "Apple",
        "language": "en"
    }
    
    print(f"URL: {url}")
    print(f"Data: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data, timeout=25)
        
        print(f"\nResponse Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Success!")
            print(f"\n🏢 Company Information:")
            print(f"   Name: {data.get('name', 'N/A')}")
            print(f"   Code: {data.get('code', 'N/A')}")
            print(f"   Email: {data.get('company_email', 'N/A')}")
            print(f"   Website: {data.get('web_url', 'N/A')}")
            print(f"   Logo: {data.get('logo', 'N/A')}")
            print(f"   Country: {data.get('country_origin', 'N/A')}")
            print(f"   Founded: {data.get('founded', 'N/A')}")
            print(f"   CEO: {data.get('ceo', 'N/A')}")
            print(f"   Employees: {data.get('employees', 'N/A')}")
            
            # Category
            category = data.get('category', {})
            if category:
                print(f"\n📂 Category:")
                print(f"   Name: {category.get('name', 'N/A')} {category.get('icon', '')}")
                print(f"   Wiki: {category.get('wikilink', 'N/A')}")
                
                desc = category.get('description', '')
                word_count = len(desc.split()) if desc else 0
                print(f"   Description: {word_count} words")
                print(f"   Text: {desc[:150]}...")
            
            # Stock data
            stock_data = data.get('stock_data', {})
            if stock_data:
                print(f"\n📊 Stock Information:")
                print(f"   52-Week Low: ${stock_data.get('last_52_weeks_low', 0)}")
                print(f"   52-Week High: ${stock_data.get('last_52_weeks_high', 0)}")
                print(f"   Market Cap: {stock_data.get('market_cap', 'N/A')}")
                print(f"   Yesterday Close: ${stock_data.get('yesterday_close', 0)}")

            # Yesterday's complete data
            yesterday_data = data.get('yesterday_data', {})
            if yesterday_data and yesterday_data.get('Date'):
                print(f"\n📈 Yesterday's Trading Data:")
                print(f"   Date: {yesterday_data.get('Date', 'N/A')[:10]}")
                print(f"   Open: ${yesterday_data.get('Open', 0):.2f}")
                print(f"   High: ${yesterday_data.get('High', 0):.2f}")
                print(f"   Low: ${yesterday_data.get('Low', 0):.2f}")
                print(f"   Close: ${yesterday_data.get('Close', 0):.2f}")
                print(f"   Volume: {yesterday_data.get('Volume', 0):,}")
            
            # 7-day data
            last_7_days = data.get('last_7_days_data', [])
            print(f"\n📊 7-Day Market Data ({len(last_7_days)} days):")

            if last_7_days:
                print("   Date                    | Open    | High    | Low     | Close   | Volume")
                print("   " + "-" * 75)

                for day_data in last_7_days[-5:]:  # Show last 5 days
                    date = day_data.get('Date', 'N/A')[:10]  # Just the date part
                    open_price = day_data.get('Open', 0)
                    high_price = day_data.get('High', 0)
                    low_price = day_data.get('Low', 0)
                    close_price = day_data.get('Close', 0)
                    volume = day_data.get('Volume', 0)

                    print(f"   {date:<23} | ${open_price:<7.2f} | ${high_price:<7.2f} | ${low_price:<7.2f} | ${close_price:<7.2f} | {volume:,}")
            
            # Performance
            search_time = data.get('search_time', 0)
            print(f"\n⏱️  Search Time: {search_time:.1f} seconds")
            
            # Validation
            validations = {
                "Has Name": bool(data.get('name')),
                "Has Stock Code": bool(data.get('code')),
                "Has Website": bool(data.get('web_url')),
                "Has Logo": bool(data.get('logo')),
                "Has Category": bool(category),
                "Has Stock Data": bool(stock_data),
                "Has Yesterday Data": bool(yesterday_data and yesterday_data.get('Date')),
                "Has 7-Day Data": len(last_7_days) > 0,
                "Has CEO Info": bool(data.get('ceo'))
            }
            
            print(f"\n🎯 Validation:")
            for check, result in validations.items():
                print(f"   {check}: {'✅' if result else '❌'}")
            
            all_good = sum(validations.values()) >= 7  # At least 7 out of 9
            print(f"\n   Overall: {'✅ EXCELLENT' if all_good else '⚠️ NEEDS WORK'}")
            
            return all_good
            
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_stock_code():
    """Test with stock code instead of company name."""
    print("\n📈 Testing Stock Code Search")
    print("=" * 30)
    
    url = "http://localhost:8000/api/books/company-search/"
    
    test_data = {
        "company_name": "GOOGL",
        "language": "en"
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=20)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Success!")
            print(f"Name: {data.get('name', 'N/A')}")
            print(f"Code: {data.get('code', 'N/A')}")
            
            category = data.get('category', {})
            stock_data = data.get('stock_data', {})
            
            print(f"Category: {category.get('name', 'N/A')} {category.get('icon', '')}")
            print(f"Market Cap: {stock_data.get('market_cap', 'N/A')}")
            
            # Check if we got good data
            has_good_data = (
                data.get('name') and
                data.get('code') and
                category.get('name')
            )
            
            print(f"Quality: {'✅ GOOD' if has_good_data else '❌ POOR'}")
            
            return has_good_data
            
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_arabic_company():
    """Test Arabic company search."""
    print("\n🌍 Testing Arabic Company Search")
    print("=" * 33)
    
    url = "http://localhost:8000/api/books/company-search/"
    
    test_data = {
        "company_name": "Microsoft",
        "language": "ar"
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=20)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Success!")
            print(f"Name: {data.get('name', 'N/A')}")
            print(f"Country: {data.get('country_origin', 'N/A')}")
            
            category = data.get('category', {})
            if category:
                print(f"Category: {category.get('name', 'N/A')} {category.get('icon', '')}")
                
                # Check if Arabic content
                def has_arabic(text):
                    if not text:
                        return False
                    arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
                    return arabic_chars > len(text) * 0.3
                
                desc = category.get('description', '')
                arabic_content = has_arabic(desc)
                
                print(f"Arabic Content: {'✅' if arabic_content else '❌'}")
                print(f"Description preview: {desc[:100]}...")
            
            return bool(category)
            
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_multiple_companies():
    """Test multiple companies quickly."""
    print("\n🏢 Testing Multiple Companies")
    print("=" * 31)
    
    url = "http://localhost:8000/api/books/company-search/"
    
    companies = ["Tesla", "Amazon", "Netflix"]
    
    results = []
    
    for company in companies:
        print(f"\n🔍 Testing {company}...")
        try:
            response = requests.post(url, json={
                "company_name": company,
                "language": "en"
            }, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                code = data.get('code', 'N/A')
                category = data.get('category', {})
                stock_data = data.get('stock_data', {})
                
                print(f"✅ Success: {code}")
                print(f"   Category: {category.get('name', 'N/A')} {category.get('icon', '')}")
                print(f"   Market Cap: {stock_data.get('market_cap', 'N/A')}")
                
                success = bool(code and category.get('name'))
                results.append(success)
            else:
                print(f"❌ Failed: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append(False)
    
    passed = sum(results)
    total = len(results)
    print(f"\n📊 Multiple Companies: {passed}/{total} passed")
    
    return passed >= total // 2

if __name__ == "__main__":
    print("🏢 QUICK COMPANY SEARCH TESTS")
    print("=" * 40)
    
    # Test Apple (detailed)
    apple_ok = test_apple_company()
    
    # Test stock code
    stock_ok = test_stock_code()
    
    # Test Arabic
    arabic_ok = test_arabic_company()
    
    # Test multiple companies
    multiple_ok = test_multiple_companies()
    
    print(f"\n🏁 QUICK TEST RESULTS")
    print("=" * 25)
    print(f"Apple Test: {'✅ PASSED' if apple_ok else '❌ FAILED'}")
    print(f"Stock Code: {'✅ PASSED' if stock_ok else '❌ FAILED'}")
    print(f"Arabic: {'✅ PASSED' if arabic_ok else '❌ FAILED'}")
    print(f"Multiple: {'✅ PASSED' if multiple_ok else '❌ FAILED'}")
    
    if apple_ok and stock_ok and arabic_ok and multiple_ok:
        print("\n🎉 COMPANY SEARCH SUCCESS!")
        print("✅ Comprehensive company information")
        print("✅ Stock data with 52-week ranges")
        print("✅ 7-day market data")
        print("✅ Company logos and branding")
        print("✅ Both company names and stock codes work")
        print("✅ Arabic language support")
    elif apple_ok or stock_ok:
        print("\n⚠️ PARTIAL SUCCESS")
        print("🔧 Some features working")
    else:
        print("\n❌ COMPANY SEARCH NEEDS WORK")
        print("🔧 Check endpoint implementation")
