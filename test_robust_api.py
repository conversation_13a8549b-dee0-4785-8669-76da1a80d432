#!/usr/bin/env python
"""
Robust test script that handles rate limiting and network issues.
"""

import os
import sys
import django
import json
import requests
import time

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_api_project.settings')
django.setup()

def count_words(text):
    """Count words in a text string."""
    if not text:
        return 0
    return len(text.split())

def test_api_with_retry(test_data, max_retries=3):
    """Test API with retry logic for rate limiting."""
    url = "http://localhost:8000/api/books/ai-search/"
    
    for attempt in range(max_retries):
        try:
            print(f"  Attempt {attempt + 1}/{max_retries}...")
            
            # Add delay between attempts to avoid rate limiting
            if attempt > 0:
                delay = 5 * attempt
                print(f"  Waiting {delay}s before retry...")
                time.sleep(delay)
            
            response = requests.post(url, json=test_data, timeout=60)
            
            if response.status_code == 200:
                return response.json(), None
            elif response.status_code == 500:
                error_data = response.json() if response.content else {"error": "Internal server error"}
                print(f"  Server error: {error_data.get('error', 'Unknown error')}")
                if attempt < max_retries - 1:
                    continue
                return None, f"Server error: {error_data.get('error', 'Unknown error')}"
            else:
                return None, f"HTTP {response.status_code}: {response.text}"
                
        except requests.exceptions.Timeout:
            print(f"  Request timed out on attempt {attempt + 1}")
            if attempt < max_retries - 1:
                continue
            return None, "Request timed out after all retries"
        except requests.exceptions.RequestException as e:
            print(f"  Request error on attempt {attempt + 1}: {e}")
            if attempt < max_retries - 1:
                continue
            return None, f"Request error: {e}"
        except Exception as e:
            print(f"  Unexpected error on attempt {attempt + 1}: {e}")
            if attempt < max_retries - 1:
                continue
            return None, f"Unexpected error: {e}"
    
    return None, "All retry attempts failed"

def test_robust_api():
    """Test API with robust error handling."""
    print("🛡️  Robust API Test")
    print("=" * 40)
    
    # Simple test cases to avoid overwhelming the API
    test_cases = [
        {
            "name": "Simple English Test",
            "data": {
                "book_name": "Pride and Prejudice",
                "language": "en",
                "max_results": 1
            }
        },
        {
            "name": "Simple Arabic Test",
            "data": {
                "book_name": "ألف ليلة وليلة",
                "language": "ar",
                "max_results": 1
            }
        }
    ]
    
    total_tests = len(test_cases)
    passed_tests = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📖 Test {i}/{total_tests}: {test_case['name']}")
        print("-" * 30)
        
        start_time = time.time()
        
        data, error = test_api_with_retry(test_case['data'])
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if error:
            print(f"❌ Failed: {error}")
            print(f"⏱️  Time: {response_time:.1f}s")
            continue
        
        if not data:
            print(f"❌ No data returned")
            continue
        
        print(f"✅ Success!")
        print(f"⏱️  Time: {response_time:.1f}s")
        
        results = data.get('results', [])
        print(f"📊 Results: {len(results)} found")
        
        # Validate results
        valid_results = 0
        for j, result in enumerate(results, 1):
            print(f"\n📚 Result {j}:")
            print(f"   Title: {result.get('title', 'N/A')}")
            
            # Check structured data
            categories = result.get('structured_categories', [])
            author = result.get('structured_author', {})
            summary = result.get('ai_book_summary', '')
            
            print(f"   Categories: {len(categories)}")
            print(f"   Author: {'✅' if author else '❌'}")
            print(f"   Summary: {'✅' if summary else '❌'}")
            
            # Check word counts if data exists
            if categories:
                cat_desc = categories[0].get('description', '')
                cat_words = count_words(cat_desc)
                print(f"   Category desc: {cat_words} words {'✅' if 50 <= cat_words <= 70 else '❌'}")
            
            if author:
                author_desc = author.get('description', '')
                author_words = count_words(author_desc)
                print(f"   Author desc: {author_words} words {'✅' if 50 <= author_words <= 70 else '❌'}")
            
            if summary:
                summary_words = count_words(summary)
                print(f"   Summary: {summary_words} words {'✅' if 80 <= summary_words <= 120 else '❌'}")
            
            # Consider result valid if it has basic structure
            if categories or author or summary:
                valid_results += 1
        
        if valid_results > 0:
            passed_tests += 1
            print(f"\n✅ Test PASSED ({valid_results} valid results)")
        else:
            print(f"\n❌ Test FAILED (no valid results)")
        
        # Add delay between tests to avoid rate limiting
        if i < total_tests:
            print("⏳ Waiting 10s before next test...")
            time.sleep(10)
    
    # Final summary
    print("\n" + "=" * 40)
    print("📋 ROBUST TEST SUMMARY")
    print("=" * 40)
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print("✅ API is working despite network/rate limit issues")
    elif passed_tests > 0:
        print("⚠️  PARTIAL SUCCESS")
        print("🔧 Some tests passed, API is functional but may have issues")
    else:
        print("❌ ALL TESTS FAILED")
        print("🔧 API has significant issues that need fixing")
    
    return passed_tests > 0

def test_direct_llm_robust():
    """Test LLM service directly with error handling."""
    print("\n🤖 Robust LLM Test")
    print("=" * 30)
    
    try:
        from books.services.llm_service import LLMService
        
        llm_service = LLMService()
        
        print("Testing English LLM...")
        start_time = time.time()
        
        en_info = llm_service.get_combined_structured_info(
            ["Fiction"], "Jane Austen", "Pride and Prejudice", "en"
        )
        
        en_time = time.time() - start_time
        print(f"⏱️  English: {en_time:.1f}s")
        
        # Check results
        categories = en_info.get('categories', [])
        author = en_info.get('author', {})
        summary = en_info.get('book_summary', '')
        
        print(f"📊 Results:")
        print(f"   Categories: {len(categories)} {'✅' if categories else '❌'}")
        print(f"   Author: {'✅' if author else '❌'}")
        print(f"   Summary: {'✅' if summary else '❌'}")
        
        if categories:
            desc_words = count_words(categories[0].get('description', ''))
            print(f"   Category desc: {desc_words} words {'✅' if 50 <= desc_words <= 70 else '❌'}")
        
        return categories or author or summary
        
    except Exception as e:
        print(f"❌ LLM test failed: {e}")
        return False

if __name__ == "__main__":
    print("🛡️  ROBUST TEST SUITE")
    print("=" * 50)
    print("This test handles rate limiting, network issues, and timeouts")
    
    # Test LLM service first
    llm_passed = test_direct_llm_robust()
    
    # Test full API
    api_passed = test_robust_api()
    
    print(f"\n🏁 FINAL RESULT")
    print("=" * 25)
    if llm_passed and api_passed:
        print("✅ ROBUST TESTS PASSED!")
        print("🛡️  API handles errors gracefully")
        print("⚡ Performance is acceptable")
    elif api_passed:
        print("⚠️  API WORKS BUT LLM ISSUES")
        print("🔧 LLM service needs attention")
    elif llm_passed:
        print("⚠️  LLM WORKS BUT API ISSUES")
        print("🔧 API integration needs attention")
    else:
        print("❌ SIGNIFICANT ISSUES")
        print("🔧 Both LLM and API need fixing")
    
    sys.exit(0 if api_passed else 1)
