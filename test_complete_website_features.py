#!/usr/bin/env python
"""
Complete test for all website search features including icons.
"""

import requests
import json

def test_complete_netflix():
    """Test Netflix with all features including website icon."""
    print("🎬 Complete Netflix Test")
    print("=" * 25)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    test_data = {
        "website_name": "Netflix",
        "language": "en"
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=25)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Success!")
            
            # Basic info
            print(f"\n📊 Basic Information:")
            print(f"   Name: {data.get('name', 'N/A')}")
            print(f"   Website Icon: {data.get('website_icon', 'N/A')}")
            print(f"   Country: {data.get('country', 'N/A')}")
            print(f"   Founded: {data.get('founded', 'N/A')}")
            print(f"   Headquarters: {data.get('headquarters', 'N/A')}")
            print(f"   Website: {data.get('website_url', 'N/A')}")
            
            # Category
            category = data.get('category', {})
            print(f"\n📂 Category:")
            print(f"   Name: {category.get('name', 'N/A')} {category.get('icon', '')}")
            print(f"   Wiki: {category.get('wikilink', 'N/A')}")
            
            cat_desc = category.get('description', '')
            cat_words = len(cat_desc.split()) if cat_desc else 0
            print(f"   Description: {cat_words} words")
            print(f"   Text: {cat_desc[:100]}...")
            
            # Descriptions
            brief = data.get('brief_description', '')
            brief_words = len(brief.split()) if brief else 0
            print(f"\n📝 Brief Description ({brief_words} words):")
            print(f"   {brief}")
            
            comp = data.get('comprehensive_description', '')
            comp_words = len(comp.split()) if comp else 0
            print(f"\n📖 Comprehensive Description ({comp_words} words):")
            print(f"   {comp[:150]}...")
            
            # App links
            app_links = data.get('app_links', {})
            print(f"\n📱 App Links:")
            print(f"   Play Store: {'✅' if app_links.get('playstore') else '❌'}")
            print(f"   App Store: {'✅' if app_links.get('appstore') else '❌'}")
            
            # Social media
            social = data.get('social_media', {})
            print(f"\n📲 Social Media:")
            for platform, link in social.items():
                status = "✅" if link and "http" in link else "❌"
                print(f"   {platform.title()}: {status}")
            
            # Validation
            validations = {
                "Has Website Icon": bool(data.get('website_icon')),
                "Icon is URL": data.get('website_icon', '').startswith('http'),
                "Broad Category": category.get('name') in ['Entertainment', 'Technology', 'E-commerce', 'Social Media', 'Education', 'Finance'],
                "Good Word Counts": 85 <= cat_words <= 95 and 35 <= brief_words <= 45 and 190 <= comp_words <= 210,
                "Has Social Links": any(social.values()),
                "Has App Links": any(app_links.values())
            }
            
            print(f"\n🎯 Validation Results:")
            for check, result in validations.items():
                print(f"   {check}: {'✅' if result else '❌'}")
            
            all_good = all(validations.values())
            print(f"\n   Overall: {'✅ EXCELLENT' if all_good else '⚠️ NEEDS WORK'}")
            
            return all_good
            
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_multiple_websites_complete():
    """Test multiple websites for complete features."""
    print("\n🌐 Multiple Websites Complete Test")
    print("=" * 35)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    websites = [
        {"name": "Google", "expected_category": "Technology"},
        {"name": "Amazon", "expected_category": "E-commerce"},
        {"name": "Facebook", "expected_category": "Social Media"}
    ]
    
    results = []
    
    for website in websites:
        print(f"\n🔍 Testing {website['name']}...")
        
        try:
            response = requests.post(url, json={
                "website_name": website['name'],
                "language": "en"
            }, timeout=20)
            
            if response.status_code == 200:
                data = response.json()
                
                # Check key features
                has_icon = bool(data.get('website_icon'))
                category_name = data.get('category', {}).get('name', '')
                has_social = any(data.get('social_media', {}).values())
                
                print(f"   Icon: {'✅' if has_icon else '❌'}")
                print(f"   Category: {category_name}")
                print(f"   Social Media: {'✅' if has_social else '❌'}")
                
                # Check if category is broad
                broad_categories = ['Entertainment', 'Technology', 'E-commerce', 'Social Media', 'Education', 'Finance']
                is_broad = any(broad in category_name for broad in broad_categories)
                
                success = has_icon and is_broad and has_social
                print(f"   Result: {'✅ GOOD' if success else '❌ INCOMPLETE'}")
                
                results.append(success)
                
            else:
                print(f"   ❌ Error: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results.append(False)
    
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Multiple Websites: {passed}/{total} passed")
    
    return passed >= total // 2

def test_arabic_complete():
    """Test Arabic with complete features."""
    print("\n🌍 Arabic Complete Test")
    print("=" * 23)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    try:
        response = requests.post(url, json={
            "website_name": "YouTube",
            "language": "ar"
        }, timeout=20)
        
        if response.status_code == 200:
            data = response.json()
            
            # Check Arabic content
            def has_arabic(text):
                if not text:
                    return False
                arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
                return arabic_chars > len(text) * 0.3
            
            category = data.get('category', {})
            brief = data.get('brief_description', '')
            
            has_icon = bool(data.get('website_icon'))
            arabic_category = has_arabic(category.get('name', ''))
            arabic_brief = has_arabic(brief)
            
            print(f"✅ Success!")
            print(f"   Name: {data.get('name', 'N/A')}")
            print(f"   Category: {category.get('name', 'N/A')} {category.get('icon', '')}")
            print(f"   Brief: {brief}")
            
            print(f"\n   Has Icon: {'✅' if has_icon else '❌'}")
            print(f"   Arabic Category: {'✅' if arabic_category else '❌'}")
            print(f"   Arabic Brief: {'✅' if arabic_brief else '❌'}")
            
            success = has_icon and arabic_category and arabic_brief
            print(f"   Result: {'✅ GOOD' if success else '❌ INCOMPLETE'}")
            
            return success
            
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🎯 COMPLETE WEBSITE SEARCH TEST")
    print("=" * 40)
    
    # Test Netflix with all features
    netflix_complete = test_complete_netflix()
    
    # Test multiple websites
    multiple_complete = test_multiple_websites_complete()
    
    # Test Arabic
    arabic_complete = test_arabic_complete()
    
    print(f"\n🏁 COMPLETE FEATURE RESULTS")
    print("=" * 30)
    print(f"Netflix Complete: {'✅ PASSED' if netflix_complete else '❌ FAILED'}")
    print(f"Multiple Websites: {'✅ PASSED' if multiple_complete else '❌ FAILED'}")
    print(f"Arabic Complete: {'✅ PASSED' if arabic_complete else '❌ FAILED'}")
    
    if netflix_complete and multiple_complete and arabic_complete:
        print("\n🎉 ALL FEATURES WORKING PERFECTLY!")
        print("✅ Website icons included")
        print("✅ Broad categories (Entertainment, Technology, etc.)")
        print("✅ Proper word counts")
        print("✅ Social media links")
        print("✅ App store links")
        print("✅ Full Arabic support")
        print("✅ No database operations")
    elif netflix_complete or multiple_complete:
        print("\n⚠️ MOSTLY WORKING")
        print("🔧 Some features need attention")
    else:
        print("\n❌ FEATURES NEED WORK")
        print("🔧 Check all components")
