#!/usr/bin/env python
"""
Quick test to verify website search fixes.
"""

import requests
import json

def test_netflix_fixed():
    """Test Netflix with the fixed endpoint."""
    print("🎬 Testing Fixed Netflix Website Search")
    print("=" * 40)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    test_data = {
        "website_name": "Netflix",
        "language": "en"
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=25)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Success!")
            print(f"\n📊 Basic Info:")
            print(f"   Name: {data.get('name', 'N/A')}")
            print(f"   Country: {data.get('country', 'N/A')}")
            print(f"   Founded: {data.get('founded', 'N/A')}")
            print(f"   Website: {data.get('website_url', 'N/A')}")
            
            # Category info
            category = data.get('category', {})
            if category:
                print(f"\n📂 Category:")
                print(f"   Name: {category.get('name', 'N/A')} {category.get('icon', '')}")
                
                desc = category.get('description', '')
                word_count = len(desc.split()) if desc else 0
                word_status = "✅" if 80 <= word_count <= 100 else "❌"
                print(f"   Description: {word_count} words {word_status}")
                print(f"   Text: {desc[:150]}...")
            
            # Brief description
            brief = data.get('brief_description', '')
            brief_words = len(brief.split()) if brief else 0
            brief_status = "✅" if 30 <= brief_words <= 50 else "❌"
            print(f"\n📝 Brief Description: {brief_words} words {brief_status}")
            print(f"   Text: {brief}")
            
            # Comprehensive description
            comp = data.get('comprehensive_description', '')
            comp_words = len(comp.split()) if comp else 0
            comp_status = "✅" if 150 <= comp_words <= 250 else "❌"
            print(f"\n📖 Comprehensive Description: {comp_words} words {comp_status}")
            print(f"   Text: {comp[:200]}...")
            
            # Social media
            social = data.get('social_media', {})
            print(f"\n📲 Social Media:")
            for platform, link in social.items():
                status = "✅" if link and "http" in link else "❌"
                print(f"   {platform.title()}: {status} {link[:50] if link else 'N/A'}")
            
            # Overall assessment
            structure_ok = all([
                data.get('name'),
                data.get('country'),
                category.get('name'),
                brief,
                comp
            ])
            
            word_counts_ok = (
                80 <= word_count <= 100 and
                30 <= brief_words <= 50 and
                150 <= comp_words <= 250
            )
            
            print(f"\n🎯 Assessment:")
            print(f"   Structure: {'✅ Complete' if structure_ok else '❌ Incomplete'}")
            print(f"   Word Counts: {'✅ Good' if word_counts_ok else '❌ Off'}")
            print(f"   Overall: {'✅ MUCH BETTER' if structure_ok and word_counts_ok else '⚠️ Still needs work'}")
            
            return structure_ok and word_counts_ok
            
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_google_fixed():
    """Test Google with the fixed endpoint."""
    print("\n🔍 Testing Fixed Google Website Search")
    print("=" * 37)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    test_data = {
        "website_name": "Google",
        "language": "en"
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=25)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Success!")
            print(f"Name: {data.get('name', 'N/A')}")
            print(f"Country: {data.get('country', 'N/A')}")
            
            category = data.get('category', {})
            if category:
                print(f"Category: {category.get('name', 'N/A')} {category.get('icon', '')}")
            
            brief = data.get('brief_description', '')
            brief_words = len(brief.split()) if brief else 0
            print(f"Brief: {brief_words} words - {brief}")
            
            # Check if we're getting real data vs fallback
            is_real_data = (
                data.get('country') != 'Unknown' and
                category.get('name') != 'Website' and
                data.get('founded') != 'Unknown'
            )
            
            print(f"Real Data: {'✅ Yes' if is_real_data else '❌ Fallback'}")
            
            return is_real_data
            
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_arabic_fixed():
    """Test Arabic with the fixed endpoint."""
    print("\n🌍 Testing Fixed Arabic Website Search")
    print("=" * 36)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    test_data = {
        "website_name": "YouTube",
        "language": "ar"
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=25)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Success!")
            print(f"Name: {data.get('name', 'N/A')}")
            print(f"Country: {data.get('country', 'N/A')}")
            
            category = data.get('category', {})
            if category:
                print(f"Category: {category.get('name', 'N/A')} {category.get('icon', '')}")
            
            brief = data.get('brief_description', '')
            print(f"Brief (Arabic): {brief}")
            
            # Check if Arabic content
            def has_arabic(text):
                if not text:
                    return False
                arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
                return arabic_chars > len(text) * 0.3
            
            arabic_ok = has_arabic(category.get('name', '')) and has_arabic(brief)
            print(f"Arabic Content: {'✅ Yes' if arabic_ok else '❌ No'}")
            
            return arabic_ok
            
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🔧 WEBSITE SEARCH FIX VERIFICATION")
    print("=" * 45)
    
    # Test the fixes
    netflix_ok = test_netflix_fixed()
    google_ok = test_google_fixed()
    arabic_ok = test_arabic_fixed()
    
    print(f"\n🏁 FIX VERIFICATION RESULTS")
    print("=" * 30)
    print(f"Netflix Test: {'✅ PASSED' if netflix_ok else '❌ FAILED'}")
    print(f"Google Test: {'✅ PASSED' if google_ok else '❌ FAILED'}")
    print(f"Arabic Test: {'✅ PASSED' if arabic_ok else '❌ FAILED'}")
    
    if netflix_ok and google_ok and arabic_ok:
        print("\n🎉 ALL FIXES WORKING!")
        print("✅ Word counts are better")
        print("✅ Real data is being returned")
        print("✅ Arabic content works")
    elif netflix_ok or google_ok:
        print("\n⚠️ PARTIAL SUCCESS")
        print("🔧 Some fixes are working")
    else:
        print("\n❌ FIXES NEED MORE WORK")
        print("🔧 Check LLM service and prompts")
