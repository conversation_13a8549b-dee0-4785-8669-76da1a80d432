#!/usr/bin/env python
"""
Comprehensive test to verify all image URLs are fixed.
"""

import requests
import json
import time

def test_multiple_cases():
    """Test multiple categories and authors."""
    
    print("=== Comprehensive Image URL Fix Test ===")
    
    # Test cases
    test_cases = [
        # Category tests
        ("category", {"category_name": "entertainment", "language": "en"}),
        ("category", {"category_name": "technology", "language": "en"}),
        ("category", {"category_name": "business", "language": "en"}),
        ("category", {"category_name": "الترفيه", "language": "ar"}),
        
        # Author tests
        ("author", {"author_name": "<PERSON>", "language": "en"}),
        ("author", {"author_name": "Shakespeare", "language": "en"}),
        ("author", {"author_name": "<PERSON>", "language": "en"}),
        ("author", {"author_name": "Unknown Author", "language": "en"}),
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, (endpoint_type, payload) in enumerate(test_cases, 1):
        print(f"\n{i}. Testing {endpoint_type}: {payload}")
        
        # Determine URL
        if endpoint_type == "category":
            url = 'http://localhost:8000/api/books/category-search/'
            image_key = 'image_url'
            name_key = 'name'
        else:
            url = 'http://localhost:8000/api/books/author-search/'
            image_key = 'author_image'
            name_key = 'name'
        
        try:
            response = requests.post(url, json=payload, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                image_url = data.get(image_key, '')
                name = data.get(name_key, 'N/A')
                
                print(f"   ✓ Status: {response.status_code}")
                print(f"   ✓ Name: {name}")
                print(f"   ✓ Image URL: {image_url}")
                
                # Check image URL quality
                if not image_url:
                    print("   ❌ FAIL: No image URL returned")
                elif 'via.placeholder.com' in image_url:
                    print("   ✅ SUCCESS: Using reliable placeholder image")
                    success_count += 1
                elif 'wikimedia.org' in image_url or 'wikipedia.org' in image_url:
                    print("   ❌ FAIL: Still using broken Wikimedia URL")
                elif 'unsplash.com' in image_url and '?' in image_url:
                    print("   ❌ FAIL: Using Unsplash URL with query params (will 404)")
                else:
                    print(f"   ⚠️  UNKNOWN: Using {image_url}")
                    # For unknown URLs, let's be conservative and not count as success
                    
            else:
                print(f"   ❌ FAIL: HTTP {response.status_code} - {response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ FAIL: Exception - {e}")
        
        # Small delay between requests
        time.sleep(1)
    
    print(f"\n=== Final Results ===")
    print(f"✅ Successful: {success_count}/{total_count}")
    print(f"❌ Failed: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 ALL TESTS PASSED! Image URL fix is working perfectly!")
    elif success_count > total_count * 0.8:
        print("✅ Most tests passed. Fix is mostly working.")
    else:
        print("❌ Many tests failed. Fix needs more work.")
    
    return success_count == total_count


if __name__ == "__main__":
    test_multiple_cases()
