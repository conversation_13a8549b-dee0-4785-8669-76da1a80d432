#!/usr/bin/env python
"""
Performance test script for the optimized AI Book Search API.
Tests response time and validates the 50-100 word descriptions.
"""

import os
import sys
import django
import json
import requests
import time

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_api_project.settings')
django.setup()

def count_words(text):
    """Count words in a text string."""
    if not text:
        return 0
    return len(text.split())

def test_performance_and_descriptions():
    """Test API performance and validate description word counts."""
    print("🚀 Performance & Description Test")
    print("=" * 50)
    
    # API endpoint
    url = "http://localhost:8000/api/books/ai-search/"
    
    # Test cases
    test_cases = [
        {
            "name": "Single Result Test",
            "data": {
                "book_name": "Pride and Prejudice",
                "language": "en",
                "max_results": 1
            }
        },
        {
            "name": "Multiple Results Test",
            "data": {
                "book_name": "The Great Gatsby",
                "language": "en",
                "max_results": 3
            }
        },
        {
            "name": "Arabic Book Test",
            "data": {
                "book_name": "ألف ليلة وليلة",
                "language": "ar",
                "max_results": 2
            }
        }
    ]
    
    total_tests = len(test_cases)
    passed_tests = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📖 Test {i}/{total_tests}: {test_case['name']}")
        print("-" * 40)
        
        # Measure response time
        start_time = time.time()
        
        try:
            response = requests.post(url, json=test_case['data'], timeout=60)
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"⏱️  Response Time: {response_time:.2f} seconds")
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                print(f"✅ Status: {response.status_code}")
                print(f"📊 Results Found: {len(results)}")
                
                # Validate each result
                for j, result in enumerate(results, 1):
                    print(f"\n📚 Result {j}:")
                    print(f"   Title: {result.get('title', 'N/A')}")
                    print(f"   Author: {result.get('author', 'N/A')}")
                    
                    # Check structured categories
                    categories = result.get('structured_categories', [])
                    print(f"   Categories: {len(categories)} found")
                    
                    for k, cat in enumerate(categories, 1):
                        cat_desc = cat.get('description', '')
                        word_count = count_words(cat_desc)
                        status = "✅" if 50 <= word_count <= 100 else "❌"
                        print(f"     {k}. {cat.get('name', 'N/A')} {cat.get('icon', '')} - {word_count} words {status}")
                        if word_count < 50 or word_count > 100:
                            print(f"        Description: {cat_desc[:100]}...")
                    
                    # Check structured author
                    author = result.get('structured_author', {})
                    if author:
                        author_desc = author.get('description', '')
                        word_count = count_words(author_desc)
                        status = "✅" if 50 <= word_count <= 100 else "❌"
                        print(f"   Author Description: {word_count} words {status}")
                        if word_count < 50 or word_count > 100:
                            print(f"     Description: {author_desc[:100]}...")
                        
                        print(f"   Author Wiki: {author.get('wikilink', 'N/A')}")
                        print(f"   Author Profession: {author.get('profession', 'N/A')}")
                    
                    print(f"   PDF Available: {'Yes' if result.get('pdf_url') else 'No'}")
                    print(f"   Source: {result.get('source_api', 'N/A')}")
                
                # Performance evaluation
                if response_time < 30:
                    print(f"\n🎯 Performance: EXCELLENT (< 30s)")
                    passed_tests += 1
                elif response_time < 45:
                    print(f"\n⚠️  Performance: GOOD (< 45s)")
                    passed_tests += 1
                elif response_time < 60:
                    print(f"\n⚠️  Performance: ACCEPTABLE (< 60s)")
                    passed_tests += 1
                else:
                    print(f"\n❌ Performance: TOO SLOW (> 60s)")
                
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        except requests.exceptions.Timeout:
            print("❌ Request timed out (> 60s)")
        except requests.exceptions.RequestException as e:
            print(f"❌ Request error: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
    
    # Final summary
    print("\n" + "=" * 50)
    print("📋 PERFORMANCE SUMMARY")
    print("=" * 50)
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! API is performing well.")
    elif passed_tests > total_tests // 2:
        print("⚠️  MOST TESTS PASSED. Some optimization may be needed.")
    else:
        print("❌ PERFORMANCE ISSUES DETECTED. Further optimization required.")
    
    return passed_tests == total_tests

def test_direct_llm_performance():
    """Test the direct LLM functions for performance."""
    print("\n🤖 Direct LLM Performance Test")
    print("=" * 40)
    
    from books.services.llm_service import LLMService
    
    llm_service = LLMService()
    
    # Test combined function performance
    print("Testing combined structured info function...")
    start_time = time.time()
    
    combined_info = llm_service.get_combined_structured_info(
        ["Fiction", "Romance"], "Jane Austen", "Pride and Prejudice", "en"
    )
    
    end_time = time.time()
    response_time = end_time - start_time
    
    print(f"⏱️  Combined LLM Call Time: {response_time:.2f} seconds")
    
    # Validate word counts
    categories = combined_info.get('categories', [])
    author = combined_info.get('author', {})
    
    print(f"📂 Categories returned: {len(categories)}")
    for cat in categories:
        word_count = count_words(cat.get('description', ''))
        status = "✅" if 50 <= word_count <= 100 else "❌"
        print(f"   {cat.get('name', 'N/A')}: {word_count} words {status}")
    
    if author:
        word_count = count_words(author.get('description', ''))
        status = "✅" if 50 <= word_count <= 100 else "❌"
        print(f"👤 Author description: {word_count} words {status}")
    
    if response_time < 5:
        print("🎯 LLM Performance: EXCELLENT")
        return True
    elif response_time < 10:
        print("⚠️  LLM Performance: GOOD")
        return True
    else:
        print("❌ LLM Performance: NEEDS IMPROVEMENT")
        return False

if __name__ == "__main__":
    print("🚀 Optimized Performance Test Suite")
    print("=" * 60)
    
    # Test direct LLM performance first
    llm_passed = test_direct_llm_performance()
    
    # Test full API performance
    api_passed = test_performance_and_descriptions()
    
    print(f"\n🏁 FINAL RESULT")
    print("=" * 30)
    if llm_passed and api_passed:
        print("✅ ALL PERFORMANCE TESTS PASSED!")
        print("🚀 API is optimized and ready for production.")
    else:
        print("⚠️  SOME PERFORMANCE ISSUES DETECTED")
        print("🔧 Consider further optimization.")
    
    sys.exit(0 if (llm_passed and api_passed) else 1)
