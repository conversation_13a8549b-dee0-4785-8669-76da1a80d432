#!/usr/bin/env python
"""
Quick test for the LLM service to verify description lengths.
"""

import os
import sys
import django

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_api_project.settings')
django.setup()

from books.services.llm_service import LLMService

def count_words(text):
    """Count words in a text string."""
    if not text:
        return 0
    return len(text.split())

def test_llm_descriptions():
    """Test LLM service for proper description lengths."""
    print("🧪 Quick LLM Description Test")
    print("=" * 40)
    
    llm_service = LLMService()
    
    # Test English
    print("\n📖 Testing English...")
    en_info = llm_service.get_combined_structured_info(
        ["Fiction", "Romance"], "Jane Austen", "Pride and Prejudice", "en"
    )
    
    print("English Results:")
    categories = en_info.get('categories', [])
    for i, cat in enumerate(categories, 1):
        desc = cat.get('description', '')
        words = count_words(desc)
        status = "✅" if 50 <= words <= 70 else "❌"
        print(f"  Category {i}: {words} words {status}")
        print(f"    Name: {cat.get('name', 'N/A')}")
        print(f"    Desc: {desc[:100]}...")
    
    author = en_info.get('author', {})
    if author:
        desc = author.get('description', '')
        words = count_words(desc)
        status = "✅" if 50 <= words <= 70 else "❌"
        print(f"  Author: {words} words {status}")
        print(f"    Desc: {desc[:100]}...")
    
    summary = en_info.get('book_summary', '')
    if summary:
        words = count_words(summary)
        status = "✅" if 80 <= words <= 120 else "❌"
        print(f"  Summary: {words} words {status}")
        print(f"    Text: {summary[:100]}...")
    
    # Test Arabic
    print("\n📖 Testing Arabic...")
    ar_info = llm_service.get_combined_structured_info(
        ["أدب", "رومانسية"], "جين أوستن", "كبرياء وتحامل", "ar"
    )
    
    print("Arabic Results:")
    categories = ar_info.get('categories', [])
    for i, cat in enumerate(categories, 1):
        desc = cat.get('description', '')
        words = count_words(desc)
        status = "✅" if 50 <= words <= 70 else "❌"
        print(f"  Category {i}: {words} words {status}")
        print(f"    Name: {cat.get('name', 'N/A')}")
        print(f"    Desc: {desc[:100]}...")
    
    author = ar_info.get('author', {})
    if author:
        desc = author.get('description', '')
        words = count_words(desc)
        status = "✅" if 50 <= words <= 70 else "❌"
        print(f"  Author: {words} words {status}")
        print(f"    Desc: {desc[:100]}...")
    
    summary = ar_info.get('book_summary', '')
    if summary:
        words = count_words(summary)
        status = "✅" if 80 <= words <= 120 else "❌"
        print(f"  Summary: {words} words {status}")
        print(f"    Text: {summary[:100]}...")

if __name__ == "__main__":
    test_llm_descriptions()
