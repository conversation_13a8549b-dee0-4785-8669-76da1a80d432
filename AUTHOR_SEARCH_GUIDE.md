# Author Search API Endpoint

## 👤 **Purpose**

The `/api/books/author-search/` endpoint provides comprehensive information about authors and writers **without any database operations**. Perfect for getting detailed author profiles, biographies, and professional information.

## 🚀 **Usage**

### Basic Request
```bash
curl -X POST http://localhost:8000/api/books/author-search/ \
  -H "Content-Type: application/json" \
  -d '{
    "author_name": "<PERSON>",
    "language": "en"
  }'
```

### Arabic Request
```bash
curl -X POST http://localhost:8000/api/books/author-search/ \
  -H "Content-Type: application/json" \
  -d '{
    "author_name": "نجيب محفوظ",
    "language": "ar"
  }'
```

## 📊 **Response Structure**

### English Response
```json
{
    "name": "<PERSON>",
    "author_image": "https://images.gr-assets.com/authors/stephen-king.jpg",
    "bio": "<PERSON> is an American author of horror, supernatural fiction, suspense, crime, science-fiction, and fantasy novels. Described as the King of Horror, a play on his surname and a reference to his high standing in pop culture, his books have sold more than 350 million copies, and many have been adapted into films, television series, miniseries, and comic books. <PERSON> has published 64 novels, including seven under the pen name <PERSON> <PERSON>man, and five non-fiction books. He has also written approximately 200 short stories, most of which have been published in book collections. His work has earned him numerous awards and honors, including the Bram Stoker Award, World Fantasy Award, and British Fantasy Society Award. King's influence on modern horror and popular culture is immeasurable, inspiring countless writers and filmmakers worldwide.",
    "professions": ["Writer", "Novelist", "Screenwriter", "Producer"],
    "wikilink": "https://en.wikipedia.org/wiki/Stephen_King",
    "youtube_link": "https://youtube.com/@stephenking",
    "birth_year": "1947",
    "nationality": "American",
    "notable_works": ["The Shining", "It", "The Stand", "Carrie", "Pet Sematary"],
    "search_time": 8.2,
    "language": "en",
    "note": "Author information without database storage"
}
```

### Arabic Response
```json
{
    "name": "نجيب محفوظ",
    "author_image": "https://images.gr-assets.com/authors/naguib-mahfouz.jpg",
    "bio": "نجيب محفوظ كاتب وروائي مصري، يُعتبر من أهم الكتاب العرب في القرن العشرين. وُلد في القاهرة عام 1911 وتوفي عام 2006. حصل على جائزة نوبل للآداب عام 1988، ليصبح أول كاتب عربي يحصل على هذه الجائزة المرموقة. كتب محفوظ أكثر من 30 رواية و350 قصة قصيرة، وتُرجمت أعماله إلى أكثر من 40 لغة. اشتهر بثلاثيته الشهيرة بين القصرين وقصر الشوق والسكرية، التي تصور الحياة في القاهرة في النصف الأول من القرن العشرين. تناولت رواياته قضايا اجتماعية وسياسية مهمة، وصور من خلالها التطورات التي شهدتها مصر والعالم العربي. يُعتبر محفوظ رائد الرواية العربية الحديثة ومؤسس المدرسة الواقعية في الأدب العربي.",
    "professions": ["كاتب", "روائي", "أديب", "صحفي"],
    "wikilink": "https://ar.wikipedia.org/wiki/نجيب_محفوظ",
    "youtube_link": "",
    "birth_year": "1911",
    "nationality": "مصري",
    "notable_works": ["بين القصرين", "قصر الشوق", "السكرية", "أولاد حارتنا"],
    "search_time": 9.1,
    "language": "ar",
    "note": "معلومات المؤلف بدون تخزين في قاعدة البيانات"
}
```

## ✨ **Features**

### 👤 **Author Information**
- **Name**: Full author name
- **Photo**: Author image URL when available
- **Birth Year**: Year of birth
- **Nationality**: Country/nationality
- **Bio**: Comprehensive 200-word biography

### 💼 **Professional Details**
- **Professions**: Multiple roles (Writer, Novelist, Poet, Professor, Journalist, etc.)
- **Notable Works**: List of famous books and publications
- **Wikipedia Link**: Official Wikipedia page
- **YouTube Channel**: Official YouTube channel (if available)

### 🌍 **Language Support**
- **English**: All content in English
- **Arabic**: All content in Arabic including professions and bio
- **Consistent**: Language-specific Wikipedia links

### 🚫 **No Database Operations**
- Pure search results without any storage
- No models, serializers, or CRUD operations
- Stateless API responses

## 📝 **Request Parameters**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `author_name` | string | Yes | Name of the author to search |
| `language` | string | No | Language preference ("en" or "ar", default: "en") |

## 📊 **Response Fields**

| Field | Type | Description |
|-------|------|-------------|
| `name` | string | Author's full name |
| `author_image` | string | URL to author's photo |
| `bio` | string | 200-word biography |
| `professions` | array | List of professional roles |
| `wikilink` | string | Wikipedia page URL |
| `youtube_link` | string | YouTube channel URL (if available) |
| `birth_year` | string | Year of birth |
| `nationality` | string | Author's nationality |
| `notable_works` | array | List of famous works |
| `search_time` | number | API response time in seconds |
| `language` | string | Response language |

## 🧪 **Testing**

### Test Scripts
```bash
# Quick test
python test_quick_author.py

# Comprehensive test
python test_author_search.py
```

### Manual Testing
```bash
# Test Stephen King
curl -X POST http://localhost:8000/api/books/author-search/ \
  -H "Content-Type: application/json" \
  -d '{"author_name": "Stephen King", "language": "en"}'

# Test Arabic author
curl -X POST http://localhost:8000/api/books/author-search/ \
  -H "Content-Type: application/json" \
  -d '{"author_name": "نجيب محفوظ", "language": "ar"}'
```

## 🚨 **Error Handling**

### Error Responses
```json
// Empty author name
{
    "error": "author_name is required"
}

// Invalid language
{
    "error": "Language must be \"en\" or \"ar\""
}

// Missing author name
{
    "error": "author_name is required"
}
```

## 📈 **Performance**

### Expected Response Times
- **Single author**: < 15 seconds
- **Complex queries**: < 20 seconds
- **Fallback responses**: < 5 seconds

### Performance Features
- Zero database operations
- Direct JSON responses
- Efficient LLM prompts
- Smart fallback system

## 🎯 **Use Cases**

### Perfect For:
- ✅ Author profile pages
- ✅ Book recommendation systems
- ✅ Literary research applications
- ✅ Educational platforms
- ✅ Reading apps and websites
- ✅ Author discovery features

### Example Applications:
- Display author information on book pages
- Create author comparison tools
- Build literary timeline applications
- Generate author recommendation lists
- Create educational content about writers

## 🔧 **Integration Examples**

### JavaScript/React
```javascript
const searchAuthor = async (authorName, language = 'en') => {
    const response = await fetch('/api/books/author-search/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ author_name: authorName, language })
    });
    
    if (response.ok) {
        const authorData = await response.json();
        console.log(`${authorData.name}: ${authorData.professions.join(', ')}`);
        return authorData;
    }
};
```

### Python
```python
import requests

def search_author(author_name, language='en'):
    url = 'http://localhost:8000/api/books/author-search/'
    data = {'author_name': author_name, 'language': language}
    
    response = requests.post(url, json=data)
    if response.status_code == 200:
        author_data = response.json()
        print(f"{author_data['name']}: {', '.join(author_data['professions'])}")
        return author_data
    
    return None
```

## 🎉 **Summary**

The Author Search API provides:
- 👤 **Complete author profiles** with photos and biographies
- 💼 **Professional information** including multiple roles
- 📚 **Notable works** and achievements
- 🔗 **External links** to Wikipedia and YouTube
- 🌍 **Full language support** for Arabic and English
- 🚫 **No database complexity** - pure search results
- ⚡ **Fast performance** with smart caching and fallbacks

Perfect for any application that needs rich author information without the overhead of database management!
