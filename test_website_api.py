#!/usr/bin/env python3
"""
Test script for the website search API to verify fixes for:
1. NoneType error with .lower() calls
2. Better Arabic language handling
3. Improved social media and app store link validation
"""

import requests
import json
import sys

def test_website_search(website_name, language='en'):
    """Test the website search API endpoint"""
    url = "http://127.0.0.1:8000/api/books/website-search/"
    
    payload = {
        "website_name": website_name,
        "language": language
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"\n🔍 Testing website search for: {website_name} (language: {language})")
        print(f"URL: {url}")
        print(f"Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        print(f"\n📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Success! Response received:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # Check for specific issues
            print("\n🔍 Validation checks:")
            
            # Check for mixed language content
            if language == 'ar':
                name = data.get('name', '')
                description = data.get('comprehensive_description', '')
                if any(ord(c) < 128 for c in name + description if c.isalpha()):
                    print("⚠️  Warning: Mixed language content detected")
                else:
                    print("✅ Pure Arabic content confirmed")
            
            # Check social media links
            social_media = data.get('social_media', {})
            app_links = data.get('app_links', {})
            
            print(f"📱 Social media links: {len([v for v in social_media.values() if v])}")
            print(f"📱 App store links: {len([v for v in app_links.values() if v])}")
            
            for platform, link in social_media.items():
                if link:
                    print(f"  {platform}: {link}")
                else:
                    print(f"  {platform}: (empty)")
            
            for store, link in app_links.items():
                if link:
                    print(f"  {store}: {link}")
                else:
                    print(f"  {store}: (empty)")
                    
        else:
            print(f"❌ Error: {response.status_code}")
            try:
                error_data = response.json()
                print(json.dumps(error_data, indent=2))
            except:
                print(response.text)
                
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def main():
    """Run tests for different scenarios"""
    
    print("🚀 Starting Website Search API Tests")
    print("=" * 50)
    
    # Test cases - focus on Netflix first
    test_cases = [
        # Test with popular websites
        ("Netflix", "en"),
        # ("Netflix", "ar"),
        # ("Google", "en"),
        # ("Facebook", "ar"),
        # ("Apple", "en"),

        # Test edge cases
        # ("", "en"),  # Empty name
        # ("NonExistentWebsite123", "en"),  # Non-existent
        # ("Test", "ar"),  # Simple name in Arabic
    ]
    
    for website_name, language in test_cases:
        try:
            test_website_search(website_name, language)
        except KeyboardInterrupt:
            print("\n⏹️  Tests interrupted by user")
            break
        except Exception as e:
            print(f"❌ Test failed for {website_name} ({language}): {e}")
        
        print("\n" + "-" * 50)
    
    print("\n✅ All tests completed!")

if __name__ == "__main__":
    main()
