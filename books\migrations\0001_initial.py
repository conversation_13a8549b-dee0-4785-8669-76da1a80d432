# Generated by Django 5.2.4 on 2025-07-14 18:25

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Book',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=500, verbose_name='العنوان')),
                ('author', models.CharField(max_length=300, verbose_name='المؤلف')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('category', models.CharField(max_length=200, verbose_name='الفئة')),
                ('status', models.CharField(choices=[('published', 'منشور'), ('draft', 'مسودة'), ('pending', 'معلق')], default='draft', max_length=20, verbose_name='الحالة')),
                ('pdf_file', models.FileField(blank=True, null=True, upload_to='books/pdfs/', verbose_name='ملف')),
                ('cover_image', models.URLField(blank=True, null=True, verbose_name='غلاف الكتاب')),
                ('isbn', models.CharField(blank=True, max_length=20, null=True)),
                ('publication_date', models.DateField(blank=True, null=True)),
                ('publisher', models.CharField(blank=True, max_length=200, null=True)),
                ('language', models.CharField(default='ar', max_length=10)),
                ('ai_generated_summary', models.TextField(blank=True, null=True)),
                ('related_books', models.JSONField(blank=True, default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('view_count', models.PositiveIntegerField(default=0, verbose_name='عدد المشاهدات')),
            ],
            options={
                'verbose_name': 'كتاب',
                'verbose_name_plural': 'الكتب',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BookSearchResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('search_session', models.CharField(max_length=100)),
                ('title', models.CharField(max_length=500)),
                ('author', models.CharField(max_length=300)),
                ('description', models.TextField(blank=True, null=True)),
                ('category', models.CharField(blank=True, max_length=200, null=True)),
                ('cover_image_url', models.URLField(blank=True, null=True)),
                ('pdf_url', models.URLField(blank=True, null=True)),
                ('pdf_source', models.CharField(blank=True, max_length=100, null=True)),
                ('pdf_verified', models.BooleanField(default=False)),
                ('isbn', models.CharField(blank=True, max_length=20, null=True)),
                ('publication_date', models.CharField(blank=True, max_length=50, null=True)),
                ('publisher', models.CharField(blank=True, max_length=200, null=True)),
                ('language', models.CharField(default='en', max_length=10)),
                ('ai_summary', models.TextField(blank=True, null=True)),
                ('ai_categories', models.JSONField(blank=True, default=list)),
                ('source_api', models.CharField(max_length=50)),
                ('external_id', models.CharField(blank=True, max_length=100, null=True)),
                ('relevance_score', models.FloatField(default=0.0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-relevance_score', '-created_at'],
            },
        ),
    ]
