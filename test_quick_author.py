#!/usr/bin/env python
"""
Quick test for the author search endpoint.
"""

import requests
import json

def test_stephen_king():
    """Test Stephen King author search."""
    print("👤 Testing Stephen King Author Search")
    print("=" * 38)
    
    url = "http://localhost:8000/api/books/author-search/"
    
    test_data = {
        "author_name": "<PERSON> King",
        "language": "en"
    }
    
    print(f"URL: {url}")
    print(f"Data: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data, timeout=25)
        
        print(f"\nResponse Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Success!")
            print(f"\n👤 Author Information:")
            print(f"   Name: {data.get('name', 'N/A')}")
            print(f"   Birth Year: {data.get('birth_year', 'N/A')}")
            print(f"   Nationality: {data.get('nationality', 'N/A')}")
            print(f"   Image: {'✅' if data.get('author_image') else '❌'}")
            
            # Bio
            bio = data.get('bio', '')
            bio_words = len(bio.split()) if bio else 0
            bio_status = "✅" if 150 <= bio_words <= 250 else "❌"
            print(f"\n📖 Biography ({bio_words} words {bio_status}):")
            print(f"   {bio[:200]}...")
            
            # Professions
            professions = data.get('professions', [])
            print(f"\n💼 Professions ({len(professions)}):")
            for prof in professions:
                print(f"   • {prof}")
            
            # Notable works
            notable_works = data.get('notable_works', [])
            print(f"\n📚 Notable Works ({len(notable_works)}):")
            for work in notable_works[:5]:  # Show first 5
                print(f"   • {work}")
            
            # Links
            print(f"\n🔗 Links:")
            print(f"   Wikipedia: {data.get('wikilink', 'N/A')}")
            print(f"   YouTube: {data.get('youtube_link', 'N/A') or 'Not available'}")
            
            # Performance
            search_time = data.get('search_time', 0)
            print(f"\n⏱️  Search Time: {search_time:.1f} seconds")
            
            # Validation
            validations = {
                "Has Bio": bool(bio),
                "Bio Word Count": 150 <= bio_words <= 250,
                "Has Professions": len(professions) > 0,
                "Has Notable Works": len(notable_works) > 0,
                "Has Wikipedia": bool(data.get('wikilink')),
                "Has Image": bool(data.get('author_image'))
            }
            
            print(f"\n🎯 Validation:")
            for check, result in validations.items():
                print(f"   {check}: {'✅' if result else '❌'}")
            
            all_good = all(validations.values())
            print(f"\n   Overall: {'✅ EXCELLENT' if all_good else '⚠️ NEEDS WORK'}")
            
            return all_good
            
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_arabic_author():
    """Test Arabic author search."""
    print("\n🌍 Testing Arabic Author Search")
    print("=" * 32)
    
    url = "http://localhost:8000/api/books/author-search/"
    
    test_data = {
        "author_name": "نجيب محفوظ",
        "language": "ar"
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=25)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Success!")
            print(f"Name: {data.get('name', 'N/A')}")
            print(f"Nationality: {data.get('nationality', 'N/A')}")
            
            # Check Arabic content
            bio = data.get('bio', '')
            professions = data.get('professions', [])
            
            def has_arabic(text):
                if not text:
                    return False
                arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
                return arabic_chars > len(text) * 0.3
            
            bio_arabic = has_arabic(bio)
            prof_arabic = any(has_arabic(prof) for prof in professions)
            
            print(f"Bio in Arabic: {'✅' if bio_arabic else '❌'}")
            print(f"Professions in Arabic: {'✅' if prof_arabic else '❌'}")
            print(f"Professions: {', '.join(professions)}")
            print(f"Bio preview: {bio[:100]}...")
            
            return bio_arabic and prof_arabic
            
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_multiple_authors():
    """Test multiple authors quickly."""
    print("\n📚 Testing Multiple Authors")
    print("=" * 28)
    
    url = "http://localhost:8000/api/books/author-search/"
    
    authors = ["J.K. Rowling", "Agatha Christie", "Mark Twain"]
    
    results = []
    
    for author in authors:
        print(f"\n🔍 Testing {author}...")
        try:
            response = requests.post(url, json={
                "author_name": author,
                "language": "en"
            }, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                professions = data.get('professions', [])
                bio_words = len(data.get('bio', '').split())
                
                print(f"✅ Success: {', '.join(professions[:2])}")
                print(f"   Bio: {bio_words} words")
                
                success = len(professions) > 0 and bio_words > 100
                results.append(success)
            else:
                print(f"❌ Failed: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append(False)
    
    passed = sum(results)
    total = len(results)
    print(f"\n📊 Multiple Authors: <AUTHORS>
    
    return passed >= total // 2

if __name__ == "__main__":
    print("👤 QUICK AUTHOR SEARCH TESTS")
    print("=" * 35)
    
    # Test Stephen King (detailed)
    stephen_ok = test_stephen_king()
    
    # Test Arabic author
    arabic_ok = test_arabic_author()
    
    # Test multiple authors
    multiple_ok = test_multiple_authors()
    
    print(f"\n🏁 QUICK TEST RESULTS")
    print("=" * 25)
    print(f"Stephen King: {'✅ PASSED' if stephen_ok else '❌ FAILED'}")
    print(f"Arabic Author: {'✅ PASSED' if arabic_ok else '❌ FAILED'}")
    print(f"Multiple Authors: <AUTHORS>
    
    if stephen_ok and arabic_ok and multiple_ok:
        print("\n🎉 AUTHOR SEARCH SUCCESS!")
        print("✅ Comprehensive author information")
        print("✅ Proper bio word counts")
        print("✅ Multiple professions listed")
        print("✅ Wikipedia and image links")
        print("✅ Both English and Arabic work")
    elif stephen_ok or multiple_ok:
        print("\n⚠️ PARTIAL SUCCESS")
        print("🔧 Some features working")
    else:
        print("\n❌ AUTHOR SEARCH NEEDS WORK")
        print("🔧 Check endpoint implementation")
