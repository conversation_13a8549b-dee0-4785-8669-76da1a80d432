#!/usr/bin/env python
"""
Quick test to verify Netflix returns "Entertainment" instead of "Streaming Service".
"""

import requests
import json

def test_netflix_category():
    """Test that Netflix returns Entertainment category."""
    print("🎬 Testing Netflix Category Fix")
    print("=" * 32)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    test_data = {
        "website_name": "Netflix",
        "language": "en"
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=20)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            category = data.get('category', {})
            category_name = category.get('name', '')
            category_icon = category.get('icon', '')
            
            print(f"✅ Success!")
            print(f"Category: {category_name} {category_icon}")
            
            # Check if it's a broad category
            broad_categories = [
                'Entertainment', 'Technology', 'E-commerce', 'Social Media', 
                'Education', 'Finance', 'Healthcare', 'News', 'Gaming'
            ]
            
            specific_services = [
                'Streaming Service', 'Video Platform', 'Media Service',
                'Content Platform', 'Subscription Service'
            ]
            
            is_broad = any(broad in category_name for broad in broad_categories)
            is_specific = any(specific in category_name for specific in specific_services)
            
            print(f"\nAnalysis:")
            print(f"   Broad Category: {'✅ Yes' if is_broad else '❌ No'}")
            print(f"   Specific Service: {'❌ No' if not is_specific else '✅ Yes (BAD)'}")
            
            if is_broad and not is_specific:
                print(f"   Result: ✅ PERFECT - Got broad category!")
                return True
            elif is_broad:
                print(f"   Result: ⚠️ GOOD - Broad but also specific")
                return True
            else:
                print(f"   Result: ❌ BAD - Still too specific")
                return False
                
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_multiple_websites():
    """Test multiple websites for broad categories."""
    print("\n🌐 Testing Multiple Websites")
    print("=" * 30)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    websites = [
        {"name": "Netflix", "expected": "Entertainment"},
        {"name": "Google", "expected": "Technology"},
        {"name": "Amazon", "expected": "E-commerce"},
        {"name": "Facebook", "expected": "Social Media"}
    ]
    
    results = []
    
    for website in websites:
        print(f"\n🔍 Testing {website['name']}...")
        
        try:
            response = requests.post(url, json={
                "website_name": website['name'],
                "language": "en"
            }, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                category = data.get('category', {})
                category_name = category.get('name', '')
                
                print(f"   Got: {category_name}")
                print(f"   Expected: {website['expected']}")
                
                # Check if it contains the expected broad category
                is_correct = website['expected'].lower() in category_name.lower()
                
                print(f"   Result: {'✅ CORRECT' if is_correct else '❌ WRONG'}")
                results.append(is_correct)
                
            else:
                print(f"   ❌ Error: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results.append(False)
    
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Results: {passed}/{total} correct")
    
    return passed >= total // 2  # At least half should be correct

if __name__ == "__main__":
    print("🏷️ CATEGORY BREADTH TEST")
    print("=" * 30)
    
    # Test Netflix specifically
    netflix_ok = test_netflix_category()
    
    # Test multiple websites
    multiple_ok = test_multiple_websites()
    
    print(f"\n🏁 FINAL RESULTS")
    print("=" * 20)
    print(f"Netflix Test: {'✅ PASSED' if netflix_ok else '❌ FAILED'}")
    print(f"Multiple Test: {'✅ PASSED' if multiple_ok else '❌ FAILED'}")
    
    if netflix_ok and multiple_ok:
        print("\n🎉 BROAD CATEGORIES WORKING!")
        print("✅ Getting Entertainment, Technology, etc.")
        print("✅ Not getting Streaming Service, Search Engine, etc.")
    elif netflix_ok or multiple_ok:
        print("\n⚠️ PARTIAL SUCCESS")
        print("🔧 Some categories are broad, others need work")
    else:
        print("\n❌ STILL TOO SPECIFIC")
        print("🔧 Categories need to be broader")
