#!/usr/bin/env python
"""
Quick fix script for Groq library "proxies" parameter issue.
Run this script if you're getting: Client.__init__() got an unexpected keyword argument 'proxies'
"""

import subprocess
import sys
import importlib

def run_command(command):
    """Run a shell command and return the result."""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_groq_version():
    """Check current Groq version."""
    try:
        import groq
        version = getattr(groq, '__version__', 'unknown')
        print(f"Current Groq version: {version}")
        return version
    except ImportError:
        print("Groq is not installed")
        return None

def fix_groq_installation():
    """Fix Groq installation to the correct version."""
    print("🔧 Fixing Groq library installation...")
    
    # Step 1: Check current version
    current_version = check_groq_version()
    target_version = "0.4.1"
    
    if current_version == target_version:
        print(f"✓ Groq version {target_version} is already installed")
        return test_groq_compatibility()
    
    # Step 2: Uninstall current version
    print("📦 Uninstalling current Groq version...")
    success, stdout, stderr = run_command("pip uninstall groq -y")
    if not success:
        print(f"⚠ Warning during uninstall: {stderr}")
    
    # Step 3: Install correct version
    print(f"📦 Installing Groq version {target_version}...")
    success, stdout, stderr = run_command(f"pip install groq=={target_version}")
    
    if success:
        print(f"✓ Successfully installed Groq {target_version}")
    else:
        print(f"✗ Failed to install Groq: {stderr}")
        return False
    
    # Step 4: Verify installation
    return test_groq_compatibility()

def test_groq_compatibility():
    """Test if Groq can be imported and initialized without proxies error."""
    print("🧪 Testing Groq compatibility...")
    
    try:
        # Force reload the module to get the new version
        if 'groq' in sys.modules:
            importlib.reload(sys.modules['groq'])
        
        from groq import Groq
        
        # Test initialization (should fail with API key error, not proxies error)
        try:
            client = Groq(api_key="test_key")
            print("✓ Groq client can be initialized")
            return True
        except Exception as e:
            error_msg = str(e).lower()
            if "proxies" in error_msg:
                print(f"✗ Still getting proxies error: {e}")
                return False
            elif "api" in error_msg or "auth" in error_msg or "key" in error_msg:
                print("✓ Groq client initialization works (API key validation failed as expected)")
                return True
            else:
                print(f"⚠ Unexpected error: {e}")
                return False
                
    except ImportError as e:
        print(f"✗ Cannot import Groq: {e}")
        return False

def main():
    """Main function to fix the Groq issue."""
    print("🚀 Groq Library Fix Script")
    print("=" * 40)
    print("This script will fix the 'proxies' parameter error with Groq library.")
    print()
    
    # Check if we can test first without reinstalling
    if test_groq_compatibility():
        print("\n🎉 Groq library is working correctly!")
        print("The 'proxies' error should be resolved.")
        return True
    
    # If test fails, try to fix
    print("\n🔧 Attempting to fix Groq installation...")
    
    if fix_groq_installation():
        print("\n🎉 Groq library has been fixed!")
        print("You can now run your application:")
        print("  python manage.py runserver")
        print("\nOr test with:")
        print("  python validate_setup.py")
        return True
    else:
        print("\n❌ Failed to fix Groq library.")
        print("\nManual steps to try:")
        print("1. pip uninstall groq")
        print("2. pip install groq==0.4.1")
        print("3. python validate_setup.py")
        print("\nIf the issue persists, check for:")
        print("- Virtual environment conflicts")
        print("- Multiple Python installations")
        print("- Cached packages in pip")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
