#!/usr/bin/env python
"""
Test script to verify the new structured output format with categories and author information.
"""

import os
import sys
import django
import json
import requests

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_api_project.settings')
django.setup()

def test_structured_api_output():
    """Test the API with the new structured output format."""
    print("🧪 Testing Structured API Output...")
    
    # API endpoint
    url = "http://localhost:8000/api/books/ai-search/"
    
    # Test cases
    test_cases = [
        {
            "book_name": "Pride and Prejudice",
            "language": "en",
            "max_results": 2
        },
        {
            "book_name": "The Great Gatsby",
            "language": "en", 
            "max_results": 2
        }
    ]
    
    for i, test_data in enumerate(test_cases, 1):
        print(f"\n📖 Test Case {i}: {test_data['book_name']}")
        print("-" * 50)
        
        try:
            response = requests.post(url, json=test_data, timeout=120)
            
            if response.status_code == 200:
                data = response.json()
                
                print(f"✓ Status: {response.status_code}")
                print(f"✓ Search Session: {data.get('search_session')}")
                print(f"✓ Total Found: {data.get('total_found')}")
                
                results = data.get('results', [])
                
                for j, result in enumerate(results, 1):
                    print(f"\n📚 Result {j}:")
                    print(f"  Title: {result.get('title')}")
                    print(f"  Author: {result.get('author')}")
                    
                    # Test structured categories
                    structured_categories = result.get('structured_categories', [])
                    print(f"\n  📂 Structured Categories ({len(structured_categories)}):")
                    for cat in structured_categories:
                        print(f"    - Name: {cat.get('name')}")
                        print(f"      Icon: {cat.get('icon')}")
                        print(f"      Wiki: {cat.get('wikilink')}")
                        print(f"      Desc: {cat.get('description', '')[:50]}...")
                    
                    # Test structured author
                    structured_author = result.get('structured_author', {})
                    print(f"\n  👤 Structured Author:")
                    print(f"    - Name: {structured_author.get('name')}")
                    print(f"    - Pic: {structured_author.get('pic')}")
                    print(f"    - Wiki: {structured_author.get('wikilink')}")
                    print(f"    - Profession: {structured_author.get('profession')}")
                    print(f"    - Description: {structured_author.get('description', '')[:50]}...")
                    
                    print(f"\n  📄 Other Info:")
                    print(f"    - PDF URL: {result.get('pdf_url', 'None')}")
                    print(f"    - PDF Verified: {result.get('pdf_verified', False)}")
                    print(f"    - Source: {result.get('source_api')}")
                    print(f"    - Relevance: {result.get('relevance_score', 0)}")
                
                # Verify the expected structure
                if results:
                    first_result = results[0]
                    
                    # Check if structured_categories exists and has the right format
                    if 'structured_categories' in first_result:
                        categories = first_result['structured_categories']
                        if categories and isinstance(categories, list):
                            first_cat = categories[0]
                            required_fields = ['name', 'icon', 'wikilink', 'description']
                            if all(field in first_cat for field in required_fields):
                                print("\n✅ Structured categories format: CORRECT")
                            else:
                                print(f"\n❌ Structured categories missing fields: {required_fields}")
                        else:
                            print("\n⚠️ Structured categories is empty or not a list")
                    else:
                        print("\n❌ structured_categories field missing")
                    
                    # Check if structured_author exists and has the right format
                    if 'structured_author' in first_result:
                        author = first_result['structured_author']
                        if author and isinstance(author, dict):
                            required_fields = ['name', 'pic', 'wikilink', 'profession']
                            if all(field in author for field in required_fields):
                                print("✅ Structured author format: CORRECT")
                            else:
                                print(f"❌ Structured author missing fields: {required_fields}")
                        else:
                            print("⚠️ Structured author is empty or not a dict")
                    else:
                        print("❌ structured_author field missing")
                
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        except requests.exceptions.Timeout:
            print("❌ Request timed out")
        except requests.exceptions.RequestException as e:
            print(f"❌ Request error: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")

def test_direct_llm_functions():
    """Test the LLM functions directly."""
    print("\n🤖 Testing LLM Functions Directly...")
    
    from books.services.llm_service import LLMService
    
    llm_service = LLMService()
    
    # Test structured categories
    print("\n📂 Testing structured categories...")
    categories = ["Fiction", "Romance", "Classic Literature"]
    structured_cats = llm_service.get_structured_categories(
        categories, "Pride and Prejudice", "Jane Austen", "en"
    )
    
    print(f"Input categories: {categories}")
    print(f"Structured output: {json.dumps(structured_cats, indent=2)}")
    
    # Test structured author
    print("\n👤 Testing structured author...")
    structured_author = llm_service.get_structured_author_info(
        "Jane Austen", "Pride and Prejudice", "en"
    )
    
    print(f"Structured author: {json.dumps(structured_author, indent=2)}")

if __name__ == "__main__":
    print("🚀 Structured Output Test Suite")
    print("=" * 60)
    
    # Test direct LLM functions first
    test_direct_llm_functions()
    
    # Test full API
    test_structured_api_output()
    
    print("\n✅ Test completed!")
