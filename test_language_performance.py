#!/usr/bin/env python
"""
Test script for language-specific content and performance optimization.
Tests Arabic and English content handling with performance metrics.
"""

import os
import sys
import django
import json
import requests
import time

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_api_project.settings')
django.setup()

def count_words(text):
    """Count words in a text string."""
    if not text:
        return 0
    return len(text.split())

def is_arabic_text(text):
    """Check if text contains Arabic characters."""
    if not text:
        return False
    arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
    return arabic_chars > len(text) * 0.3  # More than 30% Arabic characters

def test_language_specific_content():
    """Test language-specific content generation."""
    print("🌍 Language-Specific Content Test")
    print("=" * 50)
    
    # API endpoint
    url = "http://localhost:8000/api/books/ai-search/"
    
    # Test cases for different languages
    test_cases = [
        {
            "name": "English Book Test",
            "data": {
                "book_name": "Pride and Prejudice",
                "language": "en",
                "max_results": 2
            },
            "expected_language": "en"
        },
        {
            "name": "Arabic Book Test",
            "data": {
                "book_name": "ألف ليلة وليلة",
                "language": "ar", 
                "max_results": 2
            },
            "expected_language": "ar"
        },
        {
            "name": "English Author Arabic Request",
            "data": {
                "book_name": "The Great Gatsby",
                "language": "ar",
                "max_results": 1
            },
            "expected_language": "ar"
        }
    ]
    
    total_tests = len(test_cases)
    passed_tests = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📖 Test {i}/{total_tests}: {test_case['name']}")
        print("-" * 40)
        
        # Measure response time
        start_time = time.time()
        
        try:
            response = requests.post(url, json=test_case['data'], timeout=45)
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"⏱️  Response Time: {response_time:.2f} seconds")
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                print(f"✅ Status: {response.status_code}")
                print(f"📊 Results Found: {len(results)}")
                
                language_correct = True
                performance_good = response_time < 30
                
                # Validate each result
                for j, result in enumerate(results, 1):
                    print(f"\n📚 Result {j}:")
                    print(f"   Title: {result.get('title', 'N/A')}")
                    print(f"   Author: {result.get('author', 'N/A')}")
                    
                    # Check AI book summary
                    book_summary = result.get('ai_book_summary', '')
                    if book_summary:
                        summary_words = count_words(book_summary)
                        is_correct_lang = (
                            is_arabic_text(book_summary) if test_case['expected_language'] == 'ar' 
                            else not is_arabic_text(book_summary)
                        )
                        lang_status = "✅" if is_correct_lang else "❌"
                        word_status = "✅" if 80 <= summary_words <= 120 else "❌"
                        
                        print(f"   📄 Book Summary: {summary_words} words {word_status} | Language: {lang_status}")
                        if not is_correct_lang:
                            language_correct = False
                            print(f"      Expected: {test_case['expected_language']}, Got: {'ar' if is_arabic_text(book_summary) else 'en'}")
                    
                    # Check structured categories
                    categories = result.get('structured_categories', [])
                    print(f"   📂 Categories: {len(categories)} found")
                    
                    for k, cat in enumerate(categories, 1):
                        cat_name = cat.get('name', '')
                        cat_desc = cat.get('description', '')
                        
                        name_correct = (
                            is_arabic_text(cat_name) if test_case['expected_language'] == 'ar' 
                            else not is_arabic_text(cat_name)
                        )
                        desc_correct = (
                            is_arabic_text(cat_desc) if test_case['expected_language'] == 'ar' 
                            else not is_arabic_text(cat_desc)
                        )
                        
                        word_count = count_words(cat_desc)
                        word_status = "✅" if 50 <= word_count <= 70 else "❌"
                        lang_status = "✅" if name_correct and desc_correct else "❌"
                        
                        print(f"     {k}. {cat_name} {cat.get('icon', '')} - {word_count} words {word_status} | Lang: {lang_status}")
                        
                        if not (name_correct and desc_correct):
                            language_correct = False
                    
                    # Check structured author
                    author = result.get('structured_author', {})
                    if author:
                        author_desc = author.get('description', '')
                        author_prof = author.get('profession', '')
                        
                        desc_correct = (
                            is_arabic_text(author_desc) if test_case['expected_language'] == 'ar' 
                            else not is_arabic_text(author_desc)
                        )
                        prof_correct = (
                            is_arabic_text(author_prof) if test_case['expected_language'] == 'ar' 
                            else not is_arabic_text(author_prof)
                        )
                        
                        word_count = count_words(author_desc)
                        word_status = "✅" if 50 <= word_count <= 70 else "❌"
                        lang_status = "✅" if desc_correct and prof_correct else "❌"
                        
                        print(f"   👤 Author: {word_count} words {word_status} | Lang: {lang_status}")
                        print(f"      Profession: {author_prof}")
                        
                        if not (desc_correct and prof_correct):
                            language_correct = False
                
                # Overall assessment
                print(f"\n🎯 Assessment:")
                print(f"   Performance: {'✅ GOOD' if performance_good else '❌ SLOW'} ({response_time:.1f}s)")
                print(f"   Language: {'✅ CORRECT' if language_correct else '❌ INCORRECT'}")
                
                if performance_good and language_correct:
                    passed_tests += 1
                    print(f"   Overall: ✅ PASSED")
                else:
                    print(f"   Overall: ❌ FAILED")
                
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        except requests.exceptions.Timeout:
            print("❌ Request timed out (> 45s)")
        except requests.exceptions.RequestException as e:
            print(f"❌ Request error: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
    
    # Final summary
    print("\n" + "=" * 50)
    print("📋 LANGUAGE & PERFORMANCE SUMMARY")
    print("=" * 50)
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Language handling is correct")
        print("✅ Performance is optimized")
    elif passed_tests > 0:
        print("⚠️  PARTIAL SUCCESS")
        print("🔧 Some optimization may be needed")
    else:
        print("❌ SIGNIFICANT ISSUES DETECTED")
        print("🔧 Language handling and performance need work")
    
    return passed_tests == total_tests

def test_direct_llm_language():
    """Test direct LLM language handling."""
    print("\n🤖 Direct LLM Language Test")
    print("=" * 40)
    
    from books.services.llm_service import LLMService
    
    llm_service = LLMService()
    
    # Test English
    print("Testing English content generation...")
    start_time = time.time()
    
    en_info = llm_service.get_combined_structured_info(
        ["Fiction", "Romance"], "Jane Austen", "Pride and Prejudice", "en"
    )
    
    en_time = time.time() - start_time
    print(f"⏱️  English LLM Time: {en_time:.2f} seconds")
    
    # Test Arabic
    print("Testing Arabic content generation...")
    start_time = time.time()
    
    ar_info = llm_service.get_combined_structured_info(
        ["أدب", "رومانسية"], "جين أوستن", "كبرياء وتحامل", "ar"
    )
    
    ar_time = time.time() - start_time
    print(f"⏱️  Arabic LLM Time: {ar_time:.2f} seconds")
    
    # Validate language correctness
    en_correct = True
    ar_correct = True
    
    # Check English content
    for cat in en_info.get('categories', []):
        if is_arabic_text(cat.get('description', '')):
            en_correct = False
            break
    
    # Check Arabic content  
    for cat in ar_info.get('categories', []):
        if not is_arabic_text(cat.get('description', '')):
            ar_correct = False
            break
    
    print(f"📊 Results:")
    print(f"   English Content: {'✅ CORRECT' if en_correct else '❌ INCORRECT'}")
    print(f"   Arabic Content: {'✅ CORRECT' if ar_correct else '❌ INCORRECT'}")
    print(f"   Performance: {'✅ GOOD' if max(en_time, ar_time) < 8 else '❌ SLOW'}")
    
    return en_correct and ar_correct and max(en_time, ar_time) < 8

if __name__ == "__main__":
    print("🚀 Language & Performance Test Suite")
    print("=" * 60)
    
    # Test direct LLM first
    llm_passed = test_direct_llm_language()
    
    # Test full API
    api_passed = test_language_specific_content()
    
    print(f"\n🏁 FINAL RESULT")
    print("=" * 30)
    if llm_passed and api_passed:
        print("✅ ALL LANGUAGE & PERFORMANCE TESTS PASSED!")
        print("🌍 Multi-language support is working correctly")
        print("⚡ Performance is optimized")
    else:
        print("⚠️  ISSUES DETECTED")
        if not llm_passed:
            print("🔧 LLM language handling needs improvement")
        if not api_passed:
            print("🔧 API performance or language handling needs work")
    
    sys.exit(0 if (llm_passed and api_passed) else 1)
