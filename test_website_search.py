#!/usr/bin/env python
"""
Test script for the website search endpoint.
"""

import os
import sys
import django
import json
import requests
import time

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_api_project.settings')
django.setup()

def count_words(text):
    """Count words in a text string."""
    if not text:
        return 0
    return len(text.split())

def is_arabic_text(text):
    """Check if text contains Arabic characters."""
    if not text:
        return False
    arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
    return arabic_chars > len(text) * 0.3

def test_website_search():
    """Test the website search endpoint."""
    print("🌐 Website Search API Test")
    print("=" * 40)
    
    # API endpoint
    url = "http://localhost:8000/api/books/website-search/"
    
    test_cases = [
        {
            "name": "Netflix (English)",
            "data": {
                "website_name": "Netflix",
                "language": "en"
            },
            "expected_category": "Streaming"
        },
        {
            "name": "Google (English)",
            "data": {
                "website_name": "Google",
                "language": "en"
            },
            "expected_category": "Search Engine"
        },
        {
            "name": "Amazon (English)",
            "data": {
                "website_name": "Amazon",
                "language": "en"
            },
            "expected_category": "E-commerce"
        },
        {
            "name": "Netflix (Arabic)",
            "data": {
                "website_name": "Netflix",
                "language": "ar"
            },
            "expected_category": "خدمة البث"
        }
    ]
    
    total_tests = len(test_cases)
    passed_tests = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Test {i}/{total_tests}: {test_case['name']}")
        print("-" * 35)
        
        start_time = time.time()
        
        try:
            response = requests.post(url, json=test_case['data'], timeout=30)
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"⏱️  Response Time: {response_time:.1f}s")
            
            if response.status_code == 200:
                data = response.json()
                
                print(f"✅ Status: {response.status_code}")
                print(f"🌐 Name: {data.get('name', 'N/A')}")
                print(f"🌍 Country: {data.get('country', 'N/A')}")
                print(f"🏢 Founded: {data.get('founded', 'N/A')}")
                print(f"📍 Headquarters: {data.get('headquarters', 'N/A')}")
                print(f"🔗 Website: {data.get('website_url', 'N/A')}")
                
                # Check category structure
                category = data.get('category', {})
                if category:
                    print(f"\n📂 Category:")
                    print(f"   Name: {category.get('name', 'N/A')} {category.get('icon', '')}")
                    print(f"   Wiki: {category.get('wikilink', 'N/A')}")
                    
                    cat_desc = category.get('description', '')
                    cat_words = count_words(cat_desc)
                    word_status = "✅" if 80 <= cat_words <= 100 else "❌"
                    print(f"   Description: {cat_words} words {word_status}")
                    print(f"   Text: {cat_desc[:100]}...")
                
                # Check descriptions
                brief_desc = data.get('brief_description', '')
                brief_words = count_words(brief_desc)
                brief_status = "✅" if 30 <= brief_words <= 50 else "❌"
                print(f"\n📝 Brief Description: {brief_words} words {brief_status}")
                print(f"   Text: {brief_desc}")
                
                comp_desc = data.get('comprehensive_description', '')
                comp_words = count_words(comp_desc)
                comp_status = "✅" if 150 <= comp_words <= 300 else "❌"
                print(f"\n📖 Comprehensive Description: {comp_words} words {comp_status}")
                print(f"   Text: {comp_desc[:150]}...")
                
                # Check app links
                app_links = data.get('app_links', {})
                print(f"\n📱 App Links:")
                print(f"   Play Store: {'✅' if app_links.get('playstore') else '❌'}")
                print(f"   App Store: {'✅' if app_links.get('appstore') else '❌'}")
                
                # Check social media links
                social_media = data.get('social_media', {})
                print(f"\n📲 Social Media:")
                for platform in ['youtube', 'instagram', 'facebook', 'twitter']:
                    link = social_media.get(platform, '')
                    status = "✅" if link else "❌"
                    print(f"   {platform.title()}: {status}")
                
                # Language validation
                expected_lang = test_case['data']['language']
                language_correct = True
                
                if expected_lang == 'ar':
                    # Check if Arabic content is present
                    if not is_arabic_text(category.get('name', '')):
                        language_correct = False
                        print(f"   ❌ Category name not in Arabic")
                    if not is_arabic_text(brief_desc):
                        language_correct = False
                        print(f"   ❌ Brief description not in Arabic")
                else:
                    # Check if English content is present
                    if is_arabic_text(category.get('name', '')):
                        language_correct = False
                        print(f"   ❌ Category name not in English")
                    if is_arabic_text(brief_desc):
                        language_correct = False
                        print(f"   ❌ Brief description not in English")
                
                # Overall assessment
                structure_valid = all([
                    data.get('name'),
                    data.get('country'),
                    category,
                    brief_desc,
                    comp_desc
                ])
                
                word_counts_valid = (
                    80 <= cat_words <= 100 and
                    30 <= brief_words <= 50 and
                    150 <= comp_words <= 300
                )
                
                print(f"\n🎯 Assessment:")
                print(f"   Response Time: {'✅ FAST' if response_time < 20 else '❌ SLOW'}")
                print(f"   Structure: {'✅ COMPLETE' if structure_valid else '❌ INCOMPLETE'}")
                print(f"   Word Counts: {'✅ CORRECT' if word_counts_valid else '❌ INCORRECT'}")
                print(f"   Language: {'✅ CORRECT' if language_correct else '❌ INCORRECT'}")
                
                if (response_time < 20 and structure_valid and 
                    word_counts_valid and language_correct):
                    passed_tests += 1
                    print(f"   Overall: ✅ PASSED")
                else:
                    print(f"   Overall: ❌ FAILED")
                
            else:
                print(f"❌ Error: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"Error details: {error_data}")
                except:
                    print(f"Response: {response.text}")
                
        except requests.exceptions.Timeout:
            print("❌ Request timed out (> 30s)")
        except requests.exceptions.RequestException as e:
            print(f"❌ Request error: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
        
        # Add delay between tests
        if i < total_tests:
            print("⏳ Waiting 8s before next test...")
            time.sleep(8)
    
    # Final summary
    print("\n" + "=" * 40)
    print("📋 WEBSITE SEARCH TEST SUMMARY")
    print("=" * 40)
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Website search endpoint works perfectly")
        print("📝 All word counts are correct")
        print("🌍 Language handling is accurate")
        print("⚡ Performance is acceptable")
        print("🔗 Social media and app links included")
    elif passed_tests > 0:
        print("⚠️  PARTIAL SUCCESS")
        print("🔧 Some tests passed, endpoint is functional")
    else:
        print("❌ ALL TESTS FAILED")
        print("🔧 Endpoint has significant issues")
    
    return passed_tests == total_tests

def test_error_handling():
    """Test error handling for the website search endpoint."""
    print("\n🚨 Error Handling Test")
    print("=" * 25)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    error_cases = [
        {
            "name": "Empty website name",
            "data": {"website_name": "", "language": "en"},
            "expected_status": 400
        },
        {
            "name": "Invalid language",
            "data": {"website_name": "Netflix", "language": "fr"},
            "expected_status": 400
        },
        {
            "name": "Missing website name",
            "data": {"language": "en"},
            "expected_status": 400
        }
    ]
    
    for case in error_cases:
        print(f"\n🧪 Testing: {case['name']}")
        try:
            response = requests.post(url, json=case['data'], timeout=10)
            if response.status_code == case['expected_status']:
                print(f"✅ Correct error handling (status {response.status_code})")
            else:
                print(f"❌ Unexpected status: {response.status_code}")
        except Exception as e:
            print(f"❌ Request failed: {e}")

def quick_website_test():
    """Quick test of popular websites."""
    print("\n🚀 Quick Website Test")
    print("=" * 25)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    websites = ["YouTube", "Facebook", "Instagram", "Twitter"]
    
    for website in websites:
        print(f"\n🔍 Testing {website}...")
        try:
            response = requests.post(url, json={
                "website_name": website,
                "language": "en"
            }, timeout=20)
            
            if response.status_code == 200:
                data = response.json()
                category = data.get('category', {})
                print(f"✅ {website}: {category.get('name', 'N/A')} {category.get('icon', '')}")
            else:
                print(f"❌ {website} failed: {response.status_code}")
        except Exception as e:
            print(f"❌ {website} error: {e}")
        
        time.sleep(2)  # Short delay

if __name__ == "__main__":
    print("🌐 WEBSITE SEARCH ENDPOINT TEST SUITE")
    print("=" * 50)
    
    # Quick test first
    quick_website_test()
    
    # Main functionality test
    main_passed = test_website_search()
    
    # Error handling test
    test_error_handling()
    
    print(f"\n🏁 FINAL RESULT")
    print("=" * 25)
    if main_passed:
        print("✅ WEBSITE SEARCH ENDPOINT SUCCESS!")
        print("🌐 Comprehensive website information")
        print("📱 App store links included")
        print("📲 Social media links provided")
        print("🌍 Full language support (Arabic/English)")
        print("📝 Proper word count compliance")
    else:
        print("⚠️  ENDPOINT NEEDS IMPROVEMENT")
        print("🔧 Check the issues identified above")
    
    sys.exit(0 if main_passed else 1)
