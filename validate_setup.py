#!/usr/bin/env python
"""
Setup Validation Script for Book API Project
Run this script to validate your environment and dependencies before running the application.
"""

import sys
import subprocess
import importlib
import os
from typing import List, <PERSON><PERSON>

def check_python_version() -> bool:
    """Check if Python version is compatible."""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✓ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"✗ Python {version.major}.{version.minor}.{version.micro} is not compatible. Requires Python 3.8+")
        return False

def check_package_version(package_name: str, expected_version: str = None) -> Tuple[bool, str]:
    """Check if a package is installed and optionally verify version."""
    try:
        module = importlib.import_module(package_name)
        installed_version = getattr(module, '__version__', 'unknown')
        
        if expected_version and installed_version != expected_version:
            return False, f"Expected {expected_version}, found {installed_version}"
        
        return True, installed_version
    except ImportError:
        return False, "Not installed"

def check_critical_packages() -> bool:
    """Check critical packages for the application."""
    print("\n📦 Checking critical packages...")
    
    critical_packages = [
        ('django', '4.2.7'),
        ('rest_framework', None),  # djangorestframework
        ('requests', '2.31.0'),
        ('groq', '0.4.1'),
        ('bs4', None),  # beautifulsoup4
    ]
    
    all_good = True
    
    for package, expected_version in critical_packages:
        is_installed, version_info = check_package_version(package, expected_version)
        
        if is_installed:
            if expected_version and version_info.startswith("Expected"):
                print(f"⚠ {package}: {version_info}")
                all_good = False
            else:
                print(f"✓ {package}: {version_info}")
        else:
            print(f"✗ {package}: {version_info}")
            all_good = False
    
    return all_good

def check_groq_compatibility() -> bool:
    """Specifically check Groq library compatibility."""
    print("\n🤖 Checking Groq library compatibility...")
    
    try:
        from groq import Groq
        
        # Try to initialize without API key to test constructor
        try:
            # This should fail with API key error, not proxies error
            client = Groq(api_key="test")
            print("✓ Groq client initialization works")
            return True
        except Exception as e:
            if "proxies" in str(e):
                print(f"✗ Groq client has proxies parameter issue: {e}")
                print("  This suggests a version mismatch or modified code.")
                return False
            elif "api_key" in str(e) or "authentication" in str(e).lower():
                print("✓ Groq client initialization works (API key validation failed as expected)")
                return True
            else:
                print(f"⚠ Groq client initialization failed with unexpected error: {e}")
                return False
                
    except ImportError as e:
        print(f"✗ Cannot import Groq: {e}")
        return False

def check_environment_variables() -> bool:
    """Check required environment variables."""
    print("\n🔧 Checking environment variables...")
    
    required_vars = ['GROQ_API_KEY']
    optional_vars = ['DEBUG', 'SECRET_KEY']
    
    all_good = True
    
    for var in required_vars:
        if var in os.environ:
            # Don't print the actual key for security
            print(f"✓ {var}: Set")
        else:
            print(f"✗ {var}: Not set (required)")
            all_good = False
    
    for var in optional_vars:
        if var in os.environ:
            print(f"✓ {var}: Set")
        else:
            print(f"⚠ {var}: Not set (optional)")
    
    return all_good

def check_django_setup() -> bool:
    """Check if Django can be set up properly."""
    print("\n🌐 Checking Django setup...")
    
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_api_project.settings')
        import django
        django.setup()
        print("✓ Django setup successful")
        return True
    except Exception as e:
        print(f"✗ Django setup failed: {e}")
        return False

def main():
    """Main validation function."""
    print("🔍 Book API Project - Setup Validation")
    print("=" * 50)
    
    checks = [
        ("Python Version", check_python_version),
        ("Critical Packages", check_critical_packages),
        ("Groq Compatibility", check_groq_compatibility),
        ("Environment Variables", check_environment_variables),
        ("Django Setup", check_django_setup),
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"✗ {check_name}: Failed with error: {e}")
            results.append((check_name, False))
    
    print("\n" + "=" * 50)
    print("📋 VALIDATION SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status}: {check_name}")
        if result:
            passed += 1
    
    print(f"\nResult: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 All checks passed! Your environment is ready.")
        print("\nYou can now run:")
        print("  python manage.py runserver")
    else:
        print("\n⚠ Some checks failed. Please fix the issues above before running the application.")
        print("\nCommon fixes:")
        print("  - Install missing packages: pip install -r requirements.txt")
        print("  - Set environment variables in .env file")
        print("  - Check Groq library version: pip show groq")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
