#!/usr/bin/env python
"""
Test script for the author search endpoint.
"""

import os
import sys
import django
import json
import requests
import time

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_api_project.settings')
django.setup()

def count_words(text):
    """Count words in a text string."""
    if not text:
        return 0
    return len(text.split())

def is_arabic_text(text):
    """Check if text contains Arabic characters."""
    if not text:
        return False
    arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
    return arabic_chars > len(text) * 0.3

def test_author_search():
    """Test the author search endpoint."""
    print("👤 Author Search API Test")
    print("=" * 30)
    
    # API endpoint
    url = "http://localhost:8000/api/books/author-search/"
    
    test_cases = [
        {
            "name": "<PERSON> (English)",
            "data": {
                "author_name": "<PERSON>",
                "language": "en"
            },
            "expected_professions": ["Writer", "Novelist"]
        },
        {
            "name": "J.K. Rowling (English)",
            "data": {
                "author_name": "J.K. Rowling",
                "language": "en"
            },
            "expected_professions": ["Writer", "Novelist"]
        },
        {
            "name": "نجيب محفوظ (Arabic)",
            "data": {
                "author_name": "نجيب محفوظ",
                "language": "ar"
            },
            "expected_professions": ["كاتب", "روائي"]
        },
        {
            "name": "Agatha Christie (English)",
            "data": {
                "author_name": "Agatha Christie",
                "language": "en"
            },
            "expected_professions": ["Writer", "Novelist"]
        }
    ]
    
    total_tests = len(test_cases)
    passed_tests = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📚 Test {i}/{total_tests}: {test_case['name']}")
        print("-" * 40)
        
        start_time = time.time()
        
        try:
            response = requests.post(url, json=test_case['data'], timeout=30)
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"⏱️  Response Time: {response_time:.1f}s")
            
            if response.status_code == 200:
                data = response.json()
                
                print(f"✅ Status: {response.status_code}")
                print(f"👤 Name: {data.get('name', 'N/A')}")
                print(f"🖼️  Image: {'✅' if data.get('author_image') else '❌'}")
                print(f"🎂 Birth Year: {data.get('birth_year', 'N/A')}")
                print(f"🌍 Nationality: {data.get('nationality', 'N/A')}")
                print(f"🔗 Wiki: {'✅' if data.get('wikilink') else '❌'}")
                print(f"📺 YouTube: {'✅' if data.get('youtube_link') else '❌'}")
                
                # Check bio
                bio = data.get('bio', '')
                bio_words = count_words(bio)
                bio_status = "✅" if 150 <= bio_words <= 250 else "❌"
                print(f"📖 Bio: {bio_words} words {bio_status}")
                print(f"   Text: {bio[:100]}...")
                
                # Check professions
                professions = data.get('professions', [])
                print(f"💼 Professions: {professions}")
                
                # Check notable works
                notable_works = data.get('notable_works', [])
                print(f"📚 Notable Works: {len(notable_works)} works")
                if notable_works:
                    print(f"   Examples: {', '.join(notable_works[:3])}")
                
                # Language validation
                expected_lang = test_case['data']['language']
                language_correct = True
                
                if expected_lang == 'ar':
                    # Check if Arabic content is present
                    if not is_arabic_text(bio):
                        language_correct = False
                        print(f"   ❌ Bio not in Arabic")
                    
                    arabic_professions = any(is_arabic_text(prof) for prof in professions)
                    if not arabic_professions:
                        language_correct = False
                        print(f"   ❌ Professions not in Arabic")
                else:
                    # Check if English content is present
                    if is_arabic_text(bio):
                        language_correct = False
                        print(f"   ❌ Bio not in English")
                    
                    english_professions = not any(is_arabic_text(prof) for prof in professions)
                    if not english_professions:
                        language_correct = False
                        print(f"   ❌ Professions not in English")
                
                # Overall assessment
                structure_valid = all([
                    data.get('name'),
                    bio,
                    professions,
                    data.get('wikilink')
                ])
                
                word_count_valid = 150 <= bio_words <= 250
                
                print(f"\n🎯 Assessment:")
                print(f"   Response Time: {'✅ FAST' if response_time < 20 else '❌ SLOW'}")
                print(f"   Structure: {'✅ COMPLETE' if structure_valid else '❌ INCOMPLETE'}")
                print(f"   Bio Word Count: {'✅ CORRECT' if word_count_valid else '❌ INCORRECT'}")
                print(f"   Language: {'✅ CORRECT' if language_correct else '❌ INCORRECT'}")
                print(f"   Professions: {len(professions)} listed")
                
                if (response_time < 20 and structure_valid and 
                    word_count_valid and language_correct and len(professions) > 0):
                    passed_tests += 1
                    print(f"   Overall: ✅ PASSED")
                else:
                    print(f"   Overall: ❌ FAILED")
                
            else:
                print(f"❌ Error: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"Error details: {error_data}")
                except:
                    print(f"Response: {response.text}")
                
        except requests.exceptions.Timeout:
            print("❌ Request timed out (> 30s)")
        except requests.exceptions.RequestException as e:
            print(f"❌ Request error: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
        
        # Add delay between tests
        if i < total_tests:
            print("⏳ Waiting 8s before next test...")
            time.sleep(8)
    
    # Final summary
    print("\n" + "=" * 30)
    print("📋 AUTHOR SEARCH TEST SUMMARY")
    print("=" * 30)
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Author search endpoint works perfectly")
        print("📖 Bio word counts are correct (150-250 words)")
        print("🌍 Language handling is accurate")
        print("💼 Professions are properly listed")
        print("🔗 Links and metadata included")
    elif passed_tests > 0:
        print("⚠️  PARTIAL SUCCESS")
        print("🔧 Some tests passed, endpoint is functional")
    else:
        print("❌ ALL TESTS FAILED")
        print("🔧 Endpoint has significant issues")
    
    return passed_tests == total_tests

def test_error_handling():
    """Test error handling for the author search endpoint."""
    print("\n🚨 Error Handling Test")
    print("=" * 25)
    
    url = "http://localhost:8000/api/books/author-search/"
    
    error_cases = [
        {
            "name": "Empty author name",
            "data": {"author_name": "", "language": "en"},
            "expected_status": 400
        },
        {
            "name": "Invalid language",
            "data": {"author_name": "Stephen King", "language": "fr"},
            "expected_status": 400
        },
        {
            "name": "Missing author name",
            "data": {"language": "en"},
            "expected_status": 400
        }
    ]
    
    for case in error_cases:
        print(f"\n🧪 Testing: {case['name']}")
        try:
            response = requests.post(url, json=case['data'], timeout=10)
            if response.status_code == case['expected_status']:
                print(f"✅ Correct error handling (status {response.status_code})")
            else:
                print(f"❌ Unexpected status: {response.status_code}")
        except Exception as e:
            print(f"❌ Request failed: {e}")

def quick_author_test():
    """Quick test of popular authors."""
    print("\n⚡ Quick Author Test")
    print("=" * 22)
    
    url = "http://localhost:8000/api/books/author-search/"
    
    authors = ["Shakespeare", "Mark Twain", "Maya Angelou"]
    
    for author in authors:
        print(f"\n🔍 Testing {author}...")
        try:
            response = requests.post(url, json={
                "author_name": author,
                "language": "en"
            }, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                professions = data.get('professions', [])
                bio_words = count_words(data.get('bio', ''))
                
                print(f"✅ {author}: {', '.join(professions[:2])}")
                print(f"   Bio: {bio_words} words")
                print(f"   Image: {'✅' if data.get('author_image') else '❌'}")
            else:
                print(f"❌ {author} failed: {response.status_code}")
        except Exception as e:
            print(f"❌ {author} error: {e}")
        
        time.sleep(2)  # Short delay

if __name__ == "__main__":
    print("👤 AUTHOR SEARCH ENDPOINT TEST SUITE")
    print("=" * 45)
    
    # Quick test first
    quick_author_test()
    
    # Main functionality test
    main_passed = test_author_search()
    
    # Error handling test
    test_error_handling()
    
    print(f"\n🏁 FINAL RESULT")
    print("=" * 20)
    if main_passed:
        print("✅ AUTHOR SEARCH ENDPOINT SUCCESS!")
        print("👤 Comprehensive author information")
        print("📖 Proper bio word counts (150-250)")
        print("💼 Multiple professions listed")
        print("🔗 Wikipedia and YouTube links")
        print("🌍 Full language support (Arabic/English)")
        print("🚫 No database operations")
    else:
        print("⚠️  ENDPOINT NEEDS IMPROVEMENT")
        print("🔧 Check the issues identified above")
    
    sys.exit(0 if main_passed else 1)
