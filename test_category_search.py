#!/usr/bin/env python
"""
Test script for the category search endpoint.
"""

import os
import sys
import django
import json
import requests
import time

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_api_project.settings')
django.setup()

def count_words(text):
    """Count words in a text string."""
    if not text:
        return 0
    return len(text.split())

def is_arabic_text(text):
    """Check if text contains Arabic characters."""
    if not text:
        return False
    arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
    return arabic_chars > len(text) * 0.3

def test_category_search():
    """Test the category search endpoint."""
    print("🏷️ Category Search API Test")
    print("=" * 30)
    
    # API endpoint
    url = "http://localhost:8000/api/books/category-search/"
    
    test_cases = [
        {
            "name": "Entertainment (English)",
            "data": {
                "category_name": "Entertainment",
                "language": "en"
            },
            "expected_icon": "🎬"
        },
        {
            "name": "Technology (English)",
            "data": {
                "category_name": "Technology",
                "language": "en"
            },
            "expected_icon": "💻"
        },
        {
            "name": "Education (English)",
            "data": {
                "category_name": "Education",
                "language": "en"
            },
            "expected_icon": "📚"
        },
        {
            "name": "الترفيه (Arabic)",
            "data": {
                "category_name": "الترفيه",
                "language": "ar"
            },
            "expected_icon": "🎬"
        }
    ]
    
    total_tests = len(test_cases)
    passed_tests = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📂 Test {i}/{total_tests}: {test_case['name']}")
        print("-" * 40)
        
        start_time = time.time()
        
        try:
            response = requests.post(url, json=test_case['data'], timeout=25)
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"⏱️  Response Time: {response_time:.1f}s")
            
            if response.status_code == 200:
                data = response.json()
                
                print(f"✅ Status: {response.status_code}")
                print(f"🏷️  Name: {data.get('name', 'N/A')}")
                print(f"🎨 Icon: {data.get('icon', 'N/A')}")
                print(f"🔗 Wiki: {'✅' if data.get('wikilink') else '❌'}")
                
                # Check description
                description = data.get('description', '')
                desc_words = count_words(description)
                desc_status = "✅" if 120 <= desc_words <= 180 else "❌"
                print(f"📝 Description: {desc_words} words {desc_status}")
                print(f"   Text: {description[:100]}...")
                
                # Check subcategories
                subcategories = data.get('subcategories', [])
                print(f"📊 Subcategories: {len(subcategories)} items")
                if subcategories:
                    print(f"   Examples: {', '.join(subcategories[:3])}")
                
                # Check related fields
                related_fields = data.get('related_fields', [])
                print(f"🔗 Related Fields: {len(related_fields)} items")
                if related_fields:
                    print(f"   Examples: {', '.join(related_fields[:3])}")
                
                # Check notable companies
                companies = data.get('notable_companies', [])
                print(f"🏢 Notable Companies: {len(companies)} items")
                if companies:
                    print(f"   Examples: {', '.join(companies[:3])}")
                
                # Check industry size
                industry_size = data.get('industry_size', '')
                print(f"💼 Industry Size: {'✅' if industry_size else '❌'}")
                if industry_size:
                    print(f"   Info: {industry_size[:80]}...")
                
                # Language validation
                expected_lang = test_case['data']['language']
                language_correct = True
                
                if expected_lang == 'ar':
                    # Check if Arabic content is present
                    if not is_arabic_text(description):
                        language_correct = False
                        print(f"   ❌ Description not in Arabic")
                    
                    arabic_subcats = any(is_arabic_text(sub) for sub in subcategories)
                    if subcategories and not arabic_subcats:
                        language_correct = False
                        print(f"   ❌ Subcategories not in Arabic")
                else:
                    # Check if English content is present
                    if is_arabic_text(description):
                        language_correct = False
                        print(f"   ❌ Description not in English")
                    
                    english_subcats = not any(is_arabic_text(sub) for sub in subcategories)
                    if subcategories and not english_subcats:
                        language_correct = False
                        print(f"   ❌ Subcategories not in English")
                
                # Overall assessment
                structure_valid = all([
                    data.get('name'),
                    data.get('icon'),
                    description,
                    data.get('wikilink'),
                    subcategories,
                    companies
                ])
                
                word_count_valid = 120 <= desc_words <= 180
                
                print(f"\n🎯 Assessment:")
                print(f"   Response Time: {'✅ FAST' if response_time < 15 else '❌ SLOW'}")
                print(f"   Structure: {'✅ COMPLETE' if structure_valid else '❌ INCOMPLETE'}")
                print(f"   Description Length: {'✅ CORRECT' if word_count_valid else '❌ INCORRECT'}")
                print(f"   Language: {'✅ CORRECT' if language_correct else '❌ INCORRECT'}")
                print(f"   Data Richness: {len(subcategories) + len(companies)} items")
                
                if (response_time < 15 and structure_valid and 
                    word_count_valid and language_correct):
                    passed_tests += 1
                    print(f"   Overall: ✅ PASSED")
                else:
                    print(f"   Overall: ❌ FAILED")
                
            else:
                print(f"❌ Error: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"Error details: {error_data}")
                except:
                    print(f"Response: {response.text}")
                
        except requests.exceptions.Timeout:
            print("❌ Request timed out (> 25s)")
        except requests.exceptions.RequestException as e:
            print(f"❌ Request error: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
        
        # Add delay between tests
        if i < total_tests:
            print("⏳ Waiting 6s before next test...")
            time.sleep(6)
    
    # Final summary
    print("\n" + "=" * 30)
    print("📋 CATEGORY SEARCH TEST SUMMARY")
    print("=" * 30)
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Category search endpoint works perfectly")
        print("📝 Description word counts are correct (120-180 words)")
        print("🌍 Language handling is accurate")
        print("🏢 Rich data with subcategories and companies")
        print("🔗 Links and metadata included")
    elif passed_tests > 0:
        print("⚠️  PARTIAL SUCCESS")
        print("🔧 Some tests passed, endpoint is functional")
    else:
        print("❌ ALL TESTS FAILED")
        print("🔧 Endpoint has significant issues")
    
    return passed_tests == total_tests

def test_error_handling():
    """Test error handling for the category search endpoint."""
    print("\n🚨 Error Handling Test")
    print("=" * 25)
    
    url = "http://localhost:8000/api/books/category-search/"
    
    error_cases = [
        {
            "name": "Empty category name",
            "data": {"category_name": "", "language": "en"},
            "expected_status": 400
        },
        {
            "name": "Invalid language",
            "data": {"category_name": "Technology", "language": "fr"},
            "expected_status": 400
        },
        {
            "name": "Missing category name",
            "data": {"language": "en"},
            "expected_status": 400
        }
    ]
    
    for case in error_cases:
        print(f"\n🧪 Testing: {case['name']}")
        try:
            response = requests.post(url, json=case['data'], timeout=10)
            if response.status_code == case['expected_status']:
                print(f"✅ Correct error handling (status {response.status_code})")
            else:
                print(f"❌ Unexpected status: {response.status_code}")
        except Exception as e:
            print(f"❌ Request failed: {e}")

def quick_category_test():
    """Quick test of popular categories."""
    print("\n⚡ Quick Category Test")
    print("=" * 23)
    
    url = "http://localhost:8000/api/books/category-search/"
    
    categories = ["Healthcare", "Finance", "Sports"]
    
    for category in categories:
        print(f"\n🔍 Testing {category}...")
        try:
            response = requests.post(url, json={
                "category_name": category,
                "language": "en"
            }, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                icon = data.get('icon', '')
                subcats = data.get('subcategories', [])
                companies = data.get('notable_companies', [])
                
                print(f"✅ {category}: {icon}")
                print(f"   Subcategories: {len(subcats)}")
                print(f"   Companies: {len(companies)}")
                if companies:
                    print(f"   Examples: {', '.join(companies[:2])}")
            else:
                print(f"❌ {category} failed: {response.status_code}")
        except Exception as e:
            print(f"❌ {category} error: {e}")
        
        time.sleep(2)  # Short delay

if __name__ == "__main__":
    print("🏷️ CATEGORY SEARCH ENDPOINT TEST SUITE")
    print("=" * 45)
    
    # Quick test first
    quick_category_test()
    
    # Main functionality test
    main_passed = test_category_search()
    
    # Error handling test
    test_error_handling()
    
    print(f"\n🏁 FINAL RESULT")
    print("=" * 20)
    if main_passed:
        print("✅ CATEGORY SEARCH ENDPOINT SUCCESS!")
        print("🏷️ Comprehensive category information")
        print("📝 Proper description word counts (120-180)")
        print("📊 Rich subcategories and related fields")
        print("🏢 Notable companies included")
        print("🌍 Full language support (Arabic/English)")
        print("🚫 No database operations")
    else:
        print("⚠️  ENDPOINT NEEDS IMPROVEMENT")
        print("🔧 Check the issues identified above")
    
    sys.exit(0 if main_passed else 1)
