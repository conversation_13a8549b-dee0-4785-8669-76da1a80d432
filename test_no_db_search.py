#!/usr/bin/env python
"""
Test script for the new AI book search endpoint without database operations.
"""

import os
import sys
import django
import json
import requests
import time

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_api_project.settings')
django.setup()

def count_words(text):
    """Count words in a text string."""
    if not text:
        return 0
    return len(text.split())

def is_arabic_text(text):
    """Check if text contains Arabic characters."""
    if not text:
        return False
    arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
    return arabic_chars > len(text) * 0.3

def test_no_db_search():
    """Test the AI search endpoint without database operations."""
    print("🚀 AI Book Search (No Database) Test")
    print("=" * 45)
    
    # API endpoint - note the new URL
    url = "http://localhost:8000/api/books/ai-search-no-db/"
    
    test_cases = [
        {
            "name": "English Classic",
            "data": {
                "book_name": "Pride and Prejudice",
                "language": "en",
                "max_results": 3
            }
        },
        {
            "name": "Arabic Literature",
            "data": {
                "book_name": "ألف ليلة وليلة",
                "language": "ar",
                "max_results": 2
            }
        },
        {
            "name": "Science Fiction",
            "data": {
                "book_name": "Dune by Frank Herbert",
                "language": "en",
                "max_results": 2
            }
        }
    ]
    
    total_tests = len(test_cases)
    passed_tests = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📚 Test {i}/{total_tests}: {test_case['name']}")
        print("-" * 35)
        
        start_time = time.time()
        
        try:
            response = requests.post(url, json=test_case['data'], timeout=45)
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"⏱️  Response Time: {response_time:.1f}s")
            
            if response.status_code == 200:
                data = response.json()
                
                print(f"✅ Status: {response.status_code}")
                print(f"📊 Results Found: {data.get('total_found', 0)}")
                print(f"🔍 Search Time: {data.get('search_time', 0):.1f}s")
                print(f"🌍 Language: {data.get('language', 'N/A')}")
                print(f"📝 Note: {data.get('note', 'N/A')}")
                
                results = data.get('results', [])
                extracted_info = data.get('extracted_info', {})
                
                print(f"\n🔍 Extracted Info:")
                print(f"   Title: {extracted_info.get('title', 'N/A')}")
                print(f"   Author: {extracted_info.get('author', 'N/A')}")
                print(f"   Categories: {extracted_info.get('categories', [])}")
                
                # Validate results structure
                structure_valid = True
                content_valid = True
                language_valid = True
                
                for j, result in enumerate(results, 1):
                    print(f"\n📖 Result {j}:")
                    print(f"   Title: {result.get('title', 'N/A')}")
                    print(f"   Author: {result.get('author', 'N/A')}")
                    
                    # Check structured data
                    structured_categories = result.get('structured_categories', [])
                    structured_author = result.get('structured_author', {})
                    ai_book_summary = result.get('ai_book_summary', '')
                    
                    print(f"   Categories: {len(structured_categories)}")
                    print(f"   Author Info: {'✅' if structured_author else '❌'}")
                    print(f"   Book Summary: {'✅' if ai_book_summary else '❌'}")
                    print(f"   PDF URL: {'✅' if result.get('pdf_url') else '❌'}")
                    print(f"   Source: {result.get('source_api', 'N/A')}")
                    
                    # Validate structure
                    required_fields = ['title', 'author', 'description']
                    if not all(result.get(field) for field in required_fields):
                        structure_valid = False
                    
                    # Check language consistency
                    expected_lang = test_case['data']['language']
                    if expected_lang == 'ar':
                        # Check if Arabic content is present
                        for cat in structured_categories:
                            if not is_arabic_text(cat.get('name', '')):
                                language_valid = False
                                break
                        
                        if structured_author and not is_arabic_text(structured_author.get('profession', '')):
                            language_valid = False
                    
                    # Check word counts
                    for cat in structured_categories:
                        desc_words = count_words(cat.get('description', ''))
                        if not (50 <= desc_words <= 70):
                            content_valid = False
                            print(f"      ⚠️  Category desc: {desc_words} words")
                    
                    if structured_author:
                        author_desc_words = count_words(structured_author.get('description', ''))
                        if not (50 <= author_desc_words <= 70):
                            content_valid = False
                            print(f"      ⚠️  Author desc: {author_desc_words} words")
                    
                    if ai_book_summary:
                        summary_words = count_words(ai_book_summary)
                        if not (80 <= summary_words <= 120):
                            content_valid = False
                            print(f"      ⚠️  Summary: {summary_words} words")
                
                # Overall assessment
                print(f"\n🎯 Assessment:")
                print(f"   Response Time: {'✅ FAST' if response_time < 30 else '❌ SLOW'}")
                print(f"   Structure: {'✅ VALID' if structure_valid else '❌ INVALID'}")
                print(f"   Content: {'✅ VALID' if content_valid else '❌ INVALID'}")
                print(f"   Language: {'✅ CONSISTENT' if language_valid else '❌ INCONSISTENT'}")
                print(f"   Results Count: {len(results)}")
                
                if (response_time < 30 and structure_valid and 
                    content_valid and len(results) > 0):
                    passed_tests += 1
                    print(f"   Overall: ✅ PASSED")
                else:
                    print(f"   Overall: ❌ FAILED")
                
            else:
                print(f"❌ Error: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"Error details: {error_data}")
                except:
                    print(f"Response: {response.text}")
                
        except requests.exceptions.Timeout:
            print("❌ Request timed out (> 45s)")
        except requests.exceptions.RequestException as e:
            print(f"❌ Request error: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
        
        # Add delay between tests
        if i < total_tests:
            print("⏳ Waiting 10s before next test...")
            time.sleep(10)
    
    # Final summary
    print("\n" + "=" * 45)
    print("📋 NO-DB SEARCH TEST SUMMARY")
    print("=" * 45)
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print("✅ No-DB search endpoint works perfectly")
        print("📝 All structured data is correct")
        print("🌍 Language handling is accurate")
        print("⚡ Performance is acceptable")
        print("🚫 No database operations performed")
    elif passed_tests > 0:
        print("⚠️  PARTIAL SUCCESS")
        print("🔧 Some tests passed, endpoint is functional")
    else:
        print("❌ ALL TESTS FAILED")
        print("🔧 Endpoint has significant issues")
    
    return passed_tests == total_tests

def test_error_handling():
    """Test error handling for the no-db endpoint."""
    print("\n🚨 Error Handling Test")
    print("=" * 25)
    
    url = "http://localhost:8000/api/books/ai-search-no-db/"
    
    error_cases = [
        {
            "name": "Empty book name",
            "data": {"book_name": "", "language": "en"},
            "expected_status": 400
        },
        {
            "name": "Invalid language",
            "data": {"book_name": "Test Book", "language": "fr"},
            "expected_status": 400
        },
        {
            "name": "Invalid max_results",
            "data": {"book_name": "Test Book", "max_results": 25},
            "expected_status": 400
        }
    ]
    
    for case in error_cases:
        print(f"\n🧪 Testing: {case['name']}")
        try:
            response = requests.post(url, json=case['data'], timeout=10)
            if response.status_code == case['expected_status']:
                print(f"✅ Correct error handling (status {response.status_code})")
            else:
                print(f"❌ Unexpected status: {response.status_code}")
        except Exception as e:
            print(f"❌ Request failed: {e}")

def compare_with_db_endpoint():
    """Compare response structure with the original DB endpoint."""
    print("\n🔄 Comparison with DB Endpoint")
    print("=" * 35)
    
    # Test data
    test_data = {
        "book_name": "Pride and Prejudice",
        "language": "en",
        "max_results": 1
    }
    
    # URLs
    no_db_url = "http://localhost:8000/api/books/ai-search-no-db/"
    db_url = "http://localhost:8000/api/books/ai-search/"
    
    print("Testing both endpoints with same data...")
    
    try:
        # Test no-db endpoint
        print("\n📊 No-DB Endpoint:")
        no_db_response = requests.post(no_db_url, json=test_data, timeout=30)
        if no_db_response.status_code == 200:
            no_db_data = no_db_response.json()
            print(f"✅ Status: {no_db_response.status_code}")
            print(f"Results: {no_db_data.get('total_found', 0)}")
            print(f"Note: {no_db_data.get('note', 'N/A')}")
        else:
            print(f"❌ Failed: {no_db_response.status_code}")
        
        # Test DB endpoint
        print("\n💾 DB Endpoint:")
        db_response = requests.post(db_url, json=test_data, timeout=30)
        if db_response.status_code == 200:
            db_data = db_response.json()
            print(f"✅ Status: {db_response.status_code}")
            print(f"Results: {len(db_data.get('results', []))}")
            print(f"Search Session: {db_data.get('search_session', 'N/A')}")
        else:
            print(f"❌ Failed: {db_response.status_code}")
        
        print("\n📋 Key Differences:")
        print("• No-DB: Returns results directly, no database storage")
        print("• DB: Saves results and returns search_session")
        print("• No-DB: Includes 'note' field and 'search_time'")
        print("• DB: Includes 'search_session' for later retrieval")
        
    except Exception as e:
        print(f"❌ Comparison failed: {e}")

if __name__ == "__main__":
    print("🚀 NO-DATABASE SEARCH ENDPOINT TEST SUITE")
    print("=" * 60)
    
    # Main functionality test
    main_passed = test_no_db_search()
    
    # Error handling test
    test_error_handling()
    
    # Comparison test
    compare_with_db_endpoint()
    
    print(f"\n🏁 FINAL RESULT")
    print("=" * 25)
    if main_passed:
        print("✅ NO-DB SEARCH ENDPOINT SUCCESS!")
        print("🚫 No database operations performed")
        print("📚 Returns same rich search results")
        print("⚡ Fast and reliable performance")
        print("🌍 Full language support maintained")
    else:
        print("⚠️  ENDPOINT NEEDS IMPROVEMENT")
        print("🔧 Check the issues identified above")
    
    sys.exit(0 if main_passed else 1)
