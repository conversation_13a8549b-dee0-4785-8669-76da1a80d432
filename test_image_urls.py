#!/usr/bin/env python
"""
Test script to verify image URL functions are working correctly.
"""

import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_api_project.settings')
django.setup()

# Now import the functions
from books.views import is_valid_image_url, search_for_reliable_image, get_image_url_from_llm

def test_image_functions():
    """Test the image URL functions."""
    
    print("Testing image URL validation...")
    
    # Test valid URLs
    valid_urls = [
        "https://images.unsplash.com/photo-1489599904472-af35ff2c7c3f?w=400&h=300&fit=crop&auto=format&fm=jpg",
        "https://www.biography.com/.image/ar_16:9%2Ccs_srgb%2Cc_fill%2Cfl_progressive%2Cg_faces:center%2Ch_675%2Cq_auto:good%2Cw_1200/MTE5NDg0MDU1MDc4NjQ1OTAz/jane-austen-9192819-1-402.jpg",
        "https://via.placeholder.com/400x300/cccccc/666666?text=Image+Not+Found"
    ]
    
    # Test invalid URLs (should be rejected)
    invalid_urls = [
        "https://upload.wikimedia.org/wikipedia/commons/thumb/c/cd/CassandraAusten-JaneAusten%28c.1810%29_hires.jpg/800px-CassandraAusten-JaneAusten%28c.1810%29_hires.jpg",
        "https://en.wikipedia.org/wiki/File:Jane_Austen.jpg",
        "https://commons.wikimedia.org/wiki/File:Jane_Austen.jpg",
        "https://example.com/not-an-image",
        ""
    ]
    
    print("\n=== Testing Valid URLs ===")
    for url in valid_urls:
        result = is_valid_image_url(url)
        print(f"✓ {result}: {url[:80]}...")
    
    print("\n=== Testing Invalid URLs (should be rejected) ===")
    for url in invalid_urls:
        result = is_valid_image_url(url)
        print(f"✗ {result}: {url[:80]}...")
    
    print("\n=== Testing Reliable Image Search ===")
    test_queries = [
        ("jane austen", "author"),
        ("shakespeare", "author"),
        ("entertainment", "category"),
        ("technology", "category"),
        ("unknown author", "author"),
        ("unknown category", "category")
    ]
    
    for query, image_type in test_queries:
        url = search_for_reliable_image(query, image_type)
        is_valid = is_valid_image_url(url)
        print(f"Query: '{query}' ({image_type}) -> Valid: {is_valid}")
        print(f"  URL: {url}")
        print()

if __name__ == "__main__":
    test_image_functions()
