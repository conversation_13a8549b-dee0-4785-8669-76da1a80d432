#!/usr/bin/env python
"""
Simple test using curl to check API endpoints.
"""

import subprocess
import json
import time

def run_curl_test():
    """Run curl test for category search."""
    print("Testing Category Search API with curl...")
    
    # Wait for server
    time.sleep(5)
    
    # Test category search
    curl_cmd = [
        'curl', '-X', 'POST',
        'http://localhost:8000/api/books/category-search/',
        '-H', 'Content-Type: application/json',
        '-d', '{"category_name": "entertainment", "language": "en"}',
        '--max-time', '30'
    ]
    
    try:
        result = subprocess.run(curl_cmd, capture_output=True, text=True, timeout=35)
        
        if result.returncode == 0:
            print("✓ Curl command successful")
            response_text = result.stdout
            print(f"Response: {response_text[:200]}...")
            
            try:
                data = json.loads(response_text)
                image_url = data.get('image_url', '')
                print(f"\nImage URL: {image_url}")
                
                if 'wikimedia.org' in image_url or 'wikipedia.org' in image_url:
                    print("✗ ERROR: Still using Wikimedia URL!")
                else:
                    print("✓ SUCCESS: Not using Wikimedia URL")
                    
            except json.JSONDecodeError:
                print("✗ ERROR: Invalid JSON response")
                print(f"Raw response: {response_text}")
        else:
            print(f"✗ Curl failed: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("✗ Curl timed out")
    except Exception as e:
        print(f"✗ Exception: {e}")


if __name__ == "__main__":
    run_curl_test()
