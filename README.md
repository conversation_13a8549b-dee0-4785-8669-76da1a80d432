# AI-Powered Book Search API

A Django REST API that uses AI (Groq LLM) to search for books and find verified PDF download links from multiple sources including Google Books, Project Gutenberg, Internet Archive, and Arabic Collections Online.

## Features

- 🤖 AI-powered book search using Groq LLM
- 📚 Multi-source book search (Google Books, Gutenberg, Internet Archive, ACO)
- ✅ PDF link verification before returning results
- 🔍 Intelligent PDF discovery from reliable sources
- 🌐 Support for both English and Arabic books
- 📊 Relevance scoring and result ranking
- 🎯 Returns only top 5 verified results

## Prerequisites

- Python 3.8 or higher
- Groq API key (get from https://console.groq.com/)

## Installation & Setup

### 1. Clone the Repository
```bash
git clone <your-repo-url>
cd book_api_project
```

### 2. Create Virtual Environment
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# macOS/Linux
python3 -m venv venv
source venv/bin/activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Configure Environment Variables
**IMPORTANT:** You must set up your Groq API key as an environment variable.

#### Option 1: Create a `.env` file (Recommended)
Create a `.env` file in the project root:
```env
GROQ_API_KEY=your_groq_api_key_here
DEBUG=True
SECRET_KEY=your-secret-key-here
```

#### Option 2: Set Environment Variables Directly
```bash
# Windows (PowerShell)
$env:GROQ_API_KEY="your_groq_api_key_here"

# Windows (Command Prompt)
set GROQ_API_KEY=your_groq_api_key_here

# macOS/Linux
export GROQ_API_KEY="your_groq_api_key_here"
```

**Note:** The application will not start without a valid GROQ_API_KEY environment variable.

### 5. Database Setup ( already done no need to do it unless any chnage in models.py )
```bash
python manage.py makemigrations
python manage.py migrate
```

### 6. Create Superuser (Optional)
```bash
python manage.py createsuperuser
```

## Running the Server

### Development Server
```bash
python manage.py runserver
```

The API will be available at: `http://localhost:8000`

### Production Server
For production, use a WSGI server like Gunicorn:
```bash
pip install gunicorn
gunicorn book_api_project.wsgi:application
```

## API Documentation

### Base URL
```
http://localhost:8000/api/books/
```

### Endpoints

#### 1. AI Book Search (with Database)
**POST** `/api/books/ai-search/`

Search for books using AI-powered query understanding and save results to database.

#### 1b. AI Book Search (No Database)
**POST** `/api/books/ai-search-no-db/`

Search for books using AI-powered analysis **WITHOUT any database operations**. Returns the same enhanced results directly without saving anything.

**Request Body:**
```json
{
    "book_name": "Pride and Prejudice",
    "language": "en",
    "max_results": 5
}
```

**Parameters:**
- `book_name` (string, required): The book title or search query
- `language` (string, optional): Language preference ("en" or "ar", default: "en")
- `max_results` (integer, optional): Maximum number of results (default: 5)

**Response:**
```json
{
    "search_session": "uuid-string",
    "results": [
        {
            "id": 1,
            "title": "Pride and Prejudice",
            "author": "Jane Austen",
            "structured_author": {
                "name": "Jane Austen",
                "pic": "/static/images/authors/jane_austen.jpg",
                "wikilink": "https://en.wikipedia.org/wiki/Jane_Austen",
                "profession": "novelist",
                "description": "English novelist known for her social commentary..."
            },
            "description": "A classic novel...",
            "category": "Fiction, Romance",
            "structured_categories": [
                {
                    "name": "Fiction",
                    "icon": "📖",
                    "wikilink": "https://en.wikipedia.org/wiki/Fiction",
                    "description": "Literature created from imagination..."
                },
                {
                    "name": "Romance",
                    "icon": "💕",
                    "wikilink": "https://en.wikipedia.org/wiki/Romance_novel",
                    "description": "Genre focusing on romantic relationships..."
                }
            ],
            "cover_image_url": "https://example.com/cover.jpg",
            "pdf_url": "https://archive.org/download/book.pdf",
            "pdf_source": "known_source",
            "pdf_verified": true,
            "pdf_verified_status": "verified",
            "isbn": "978-0-123456-78-9",
            "publication_date": "1813",
            "publisher": "Publisher Name",
            "language": "en",
            "source_api": "google_books",
            "relevance_score": 1.3,
            "created_at": "2024-01-01T12:00:00Z"
        }
    ],
    "total_found": 1,
    "extracted_info": {
        "title": "Pride and Prejudice",
        "author": "Jane Austen",
        "categories": ["Fiction", "Romance"],
        "language": "en"
    }
}
```

**New Structured Fields:**
- `structured_categories`: Array of category objects with name, icon, wiki link, and description (50-70 words)
- `structured_author`: Author object with name, picture, wiki link, profession, and description (50-70 words)
- `ai_book_summary`: AI-generated book summary (80-120 words)

**Language-Specific Content:**
- **Arabic (`language: "ar"`)**: All descriptions, summaries, categories, and professions in Arabic
- **English (`language: "en"`)**: All content in English
- Automatic language detection and appropriate Wikipedia links (ar.wikipedia.org vs en.wikipedia.org)

**No-Database Endpoint Response:**
```json
{
    "results": [...],
    "total_found": 3,
    "extracted_info": {...},
    "search_time": 15.2,
    "language": "en",
    "note": "Results returned without database storage"
}
```

**Key Benefits of No-DB Endpoint:**
- 🚫 **No database operations** - nothing saved, created, updated, or deleted
- ⚡ **Faster response** - no database overhead
- 📊 **Same rich data** - includes all structured categories, author info, summaries
- 🔍 **Performance tracking** - includes search_time metrics
- 🌍 **Full language support** - Arabic and English content

#### 2. Website/Company Search
**POST** `/api/books/website-search/`

Search for comprehensive information about websites and companies **WITHOUT any database operations**. Returns detailed company info, social media links, and app store links.

**Request Body:**
```json
{
    "website_name": "Netflix",
    "language": "en"
}
```

**Parameters:**
- `website_name` (string, required): Name of the website/company to search
- `language` (string, optional): Language preference ("en" or "ar", default: "en")

**Response:**
```json
{
    "name": "Netflix",
    "website_icon": "https://assets.nflxext.com/us/ffe/siteui/common/icons/nficon2016.ico",
    "country": "United States",
    "category": {
        "name": "Entertainment",
        "icon": "🎬",
        "wikilink": "https://en.wikipedia.org/wiki/Entertainment",
        "description": "Exactly 90 words describing the entertainment industry..."
    },
    "brief_description": "Brief 30-50 word description",
    "comprehensive_description": "Detailed 200 word description...",
    "app_links": {
        "playstore": "https://play.google.com/store/apps/details?id=...",
        "appstore": "https://apps.apple.com/app/..."
    },
    "social_media": {
        "youtube": "https://youtube.com/@netflix",
        "instagram": "https://instagram.com/netflix",
        "facebook": "https://facebook.com/netflix",
        "twitter": "https://twitter.com/netflix"
    },
    "website_url": "https://netflix.com",
    "founded": "1997",
    "headquarters": "Los Gatos, California, USA",
    "search_time": 12.3,
    "language": "en"
}
```

**Features:**
- 🌐 **Comprehensive Info**: Company details, founding info, headquarters
- 🎨 **Website Icon**: Favicon/logo URL for visual representation
- 📱 **App Store Links**: Google Play and Apple App Store links when available
- 📲 **Social Media**: YouTube, Instagram, Facebook, Twitter/X links
- 🏷️ **Broad Categories**: Industry-level categories (Entertainment, Technology, E-commerce, Social Media, Education, Finance)
- 📝 **Multiple Descriptions**: Brief (40 words) and comprehensive (200 words)
- 🌍 **Language Support**: Full Arabic and English content
- 🚫 **No Database**: Pure search results without any storage

**Category Examples:**
- Netflix → Entertainment 🎬
- Google → Technology 💻
- Amazon → E-commerce 🛒
- Facebook → Social Media 📱
- Khan Academy → Education 📚

#### 3. Author Search
**POST** `/api/books/author-search/`

Search for comprehensive information about authors and writers **WITHOUT any database operations**. Returns detailed author bio, professions, and links.

**Request Body:**
```json
{
    "author_name": "Stephen King",
    "language": "en"
}
```

**Parameters:**
- `author_name` (string, required): Name of the author to search
- `language` (string, optional): Language preference ("en" or "ar", default: "en")

**Response:**
```json
{
    "name": "Stephen King",
    "author_image": "https://images.gr-assets.com/authors/stephen-king.jpg",
    "bio": "Exactly 200 words biography covering life, career, and achievements...",
    "professions": ["Writer", "Novelist", "Screenwriter"],
    "wikilink": "https://en.wikipedia.org/wiki/Stephen_King",
    "youtube_link": "https://youtube.com/@stephenking",
    "birth_year": "1947",
    "nationality": "American",
    "notable_works": ["The Shining", "It", "The Stand", "Carrie"],
    "search_time": 8.2,
    "language": "en"
}
```

**Features:**
- 👤 **Author Details**: Name, birth year, nationality, photo
- 📖 **Biography**: Comprehensive 200-word bio covering life and career
- 💼 **Professions**: Multiple roles (Writer, Novelist, Poet, Professor, etc.)
- 🔗 **Links**: Wikipedia page and YouTube channel (if available)
- 📚 **Notable Works**: List of famous books/publications
- 🌍 **Language Support**: Full Arabic and English content
- 🚫 **No Database**: Pure search results without any storage

**Language Examples:**
- English: "Stephen King" → Writer, Novelist, Screenwriter
- Arabic: "نجيب محفوظ" → كاتب، روائي، أديب

#### 4. Category Information Search
**POST** `/api/books/category-search/`

Search for comprehensive information about any category or industry **WITHOUT any database operations**. Returns detailed category information, subcategories, and related data.

**Request Body:**
```json
{
    "category_name": "Entertainment",
    "language": "en"
}
```

**Parameters:**
- `category_name` (string, required): Name of the category to search
- `language` (string, optional): Language preference ("en" or "ar", default: "en")

**Response:**
```json
{
    "name": "Entertainment",
    "icon": "🎬",
    "wikilink": "https://en.wikipedia.org/wiki/Entertainment",
    "description": "Exactly 150 words comprehensive description of the entertainment industry, its scope, importance, and various sectors...",
    "subcategories": ["Movies", "Music", "Television", "Gaming", "Theater"],
    "related_fields": ["Media", "Arts", "Culture", "Technology"],
    "industry_size": "Global entertainment industry overview and economic impact",
    "notable_companies": ["Disney", "Netflix", "Warner Bros", "Sony Entertainment"],
    "search_time": 6.8,
    "language": "en"
}
```

**Features:**
- 🏷️ **Category Details**: Name, icon, and Wikipedia link
- 📝 **Comprehensive Description**: 150-word detailed explanation
- 📊 **Subcategories**: Main sectors within the category
- 🔗 **Related Fields**: Connected industries and areas
- 🏢 **Notable Companies**: Major players in the field
- 💼 **Industry Information**: Economic size and importance
- 🌍 **Language Support**: Full Arabic and English content
- 🚫 **No Database**: Pure search results without any storage

**Category Examples:**
- Entertainment → 🎬 Movies, Music, Television, Gaming
- Technology → 💻 Software, Hardware, AI, Cloud Computing
- Healthcare → 🏥 Hospitals, Pharmaceuticals, Medical Devices
- Education → 📚 K-12, Higher Education, Online Learning
- Finance → 💰 Banking, Insurance, Investment, Fintech

#### 5. Company/Stock Search
**POST** `/api/books/company-search/`

Search for comprehensive information about companies and stocks **WITHOUT any database operations**. Returns detailed company info, stock data, and market information.

**Request Body:**
```json
{
    "company_name": "Apple",
    "language": "en"
}
```

**Parameters:**
- `company_name` (string, required): Company name or stock ticker (e.g., "Apple" or "AAPL")
- `language` (string, optional): Language preference ("en" or "ar", default: "en")

**Response:**
```json
{
    "name": "Apple Inc.",
    "code": "AAPL",
    "company_email": "<EMAIL>",
    "web_url": "https://www.apple.com",
    "logo": "https://logo.clearbit.com/apple.com",
    "country_origin": "United States",
    "category": {
        "name": "Technology",
        "icon": "💻",
        "wikilink": "https://en.wikipedia.org/wiki/Technology",
        "description": "100 words about the technology industry..."
    },
    "founded": "1976",
    "headquarters": "Cupertino, California",
    "ceo": "Tim Cook",
    "employees": "164,000",
    "stock_data": {
        "last_52_weeks_low": 164.08,
        "last_52_weeks_high": 237.49,
        "market_cap": "3.2T",
        "yesterday_close": 210.02
    },
    "yesterday_data": {
        "Date": "2025-07-17T00:00:00-04:00",
        "Open": 210.57,
        "High": 211.80,
        "Low": 209.59,
        "Close": 210.02,
        "Volume": 48010700
    },
    "last_7_days_data": [
        {
            "Date": "2025-07-17T00:00:00-04:00",
            "Open": 210.57,
            "High": 211.80,
            "Low": 209.59,
            "Close": 210.02,
            "Volume": 48010700
        }
    ],
    "search_time": 12.4,
    "language": "en"
}
```

**Features:**
- 🏢 **Company Details**: Name, ticker, email, website, logo, headquarters
- 👨‍💼 **Leadership**: CEO and employee count information
- 🏷️ **Industry Category**: Structured category with icon and description
- 📊 **Stock Data**: 52-week high/low, market cap, yesterday's close
- 📈 **Yesterday's Trading**: Complete OHLCV data for previous trading day
- 📈 **Market History**: Last 7 days of trading data (OHLCV)
- 🌍 **Language Support**: Full Arabic and English content
- 🚫 **No Database**: Pure search results without any storage

**Input Flexibility:**
- Company Name: "Apple", "Google", "Microsoft"
- Stock Ticker: "AAPL", "GOOGL", "MSFT"
- Works with both formats seamlessly

#### 6. Analyze Book Description
**POST** `/api/books/analyze-description/`

Analyze a book description and return categorized information with detailed category descriptions.

**Request Body:**
```json
{
    "description": "A timeless tale of love, pride, and social class in 19th century England...",
    "language": "en"
}
```

**Parameters:**
- `description` (string, required): The book description text to analyze (minimum 20 characters)
- `language` (string, optional): Language preference ("en" or "ar", default: "en")

**Response:**
```json
{
    "categories": [
        {
            "name": "Fiction",
            "icon": "📖",
            "wikilink": "https://en.wikipedia.org/wiki/Fiction",
            "description": "Exactly 100 words describing the category in detail..."
        },
        {
            "name": "Romance",
            "icon": "💕",
            "wikilink": "https://en.wikipedia.org/wiki/Romance_novel",
            "description": "Exactly 100 words describing the romance category..."
        }
    ],
    "analysis_summary": "Brief summary of why these categories were chosen",
    "input_description": "Original description provided",
    "language": "en",
    "total_categories": 2
}
```

**Features:**
- **AI-Powered Analysis**: Uses LLM to identify 1-3 most relevant categories
- **Detailed Descriptions**: Each category includes exactly 100-word description
- **Language-Specific**: Returns content in requested language (Arabic/English)
- **Wikipedia Links**: Provides appropriate language-specific Wikipedia links
- **Category Icons**: Includes relevant emojis for visual representation

#### 3. Add Book from Search Results
**POST** `/api/books/add-from-search/`

Add a selected book from search results to the main database.

**Request Body:**
```json
{
    "search_result_id": 1,
    "status": "published",
    "custom_category": "Classic Literature",
    "download_pdf": true
}
```

#### 4. Get Search Results
**GET** `/api/books/search-results/{search_session}/`

Retrieve search results for a specific search session.

#### 5. Verify PDF Link
**POST** `/api/books/verify-pdf/`

Verify if a PDF link is accessible.

**Request Body:**
```json
{
    "pdf_url": "https://example.com/book.pdf"
}
```

#### 6. List Books
**GET** `/api/books/`

Get all books in the database.

#### 6. Get Book Details
**GET** `/api/books/{book_id}/`

Get details of a specific book.

## Testing

### Run Tests
```bash
python manage.py test
```

### Test API Endpoints
Use the provided test scripts:

```bash
# Test PDF enhancement functionality
python test_verified_pdf_search.py

# Test API endpoint
python test_api_endpoint.py
```

### Manual Testing with cURL

```bash
# Search for books
curl -X POST http://localhost:8000/api/books/ai-search/ \
  -H "Content-Type: application/json" \
  -d '{
    "book_name": "The Great Gatsby",
    "language": "en",
    "max_results": 3
  }'
```

## Configuration

### Groq API Settings
Update in `book_api_project/settings.py`:
```python
GROQ_API_KEY = 'your_api_key_here'
```

### File Upload Settings
```python
FILE_UPLOAD_MAX_MEMORY_SIZE = 50 * 1024 * 1024  # 50MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 50 * 1024 * 1024  # 50MB
```

### CORS Settings
```python
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True
```

## Troubleshooting

### Common Issues

1. **Groq API Key Error**
   - Ensure your Groq API key is valid and properly set
   - Check API rate limits

2. **PDF Verification Timeouts**
   - Some PDF links may timeout during verification
   - The system will try multiple sources automatically

3. **Network Issues**
   - Ensure internet connection for external API calls
   - Some sources (like gutendx.com) may be temporarily unavailable

### Debug Mode
Enable debug logging by setting `DEBUG = True` in settings.py

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
