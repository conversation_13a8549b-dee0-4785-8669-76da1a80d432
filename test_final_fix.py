#!/usr/bin/env python
"""
Final test to verify the image URL fix is working.
"""

import requests
import json
import time

def test_api_endpoints():
    """Test both category and author search endpoints."""
    
    print("=== Testing Fixed Image URLs ===")
    print("Waiting for Django server to be ready...")
    time.sleep(3)
    
    # Test Category Search
    print("\n1. Testing Category Search:")
    try:
        response = requests.post(
            'http://localhost:8000/api/books/category-search/',
            json={"category_name": "entertainment", "language": "en"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            image_url = data.get('image_url', '')
            print(f"✓ Status: {response.status_code}")
            print(f"✓ Category: {data.get('name', 'N/A')}")
            print(f"✓ Image URL: {image_url}")
            
            # Check image URL
            if not image_url:
                print("❌ No image URL returned")
            elif any(domain in image_url for domain in ['cdn.britannica.com', 'researchgate.net', 'poetryfoundation.org', 'images.unsplash.com', 'cdn.pixabay.com', 'images.pexels.com']):
                print("✅ SUCCESS: Using actual photo from Google Images search (reliable)")
            elif 'placehold.co' in image_url or 'dummyimage.com' in image_url:
                print("✅ SUCCESS: Using reliable placeholder image")
            elif 'wikimedia.org' in image_url or 'wikipedia.org' in image_url:
                print("❌ ERROR: Still using broken Wikimedia URL")
            elif 'unsplash.com' in image_url and '?' in image_url:
                print("❌ ERROR: Using Unsplash URL with query params (will 404)")
            else:
                print(f"⚠️  UNKNOWN: Using {image_url}")
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test Author Search
    print("\n2. Testing Author Search:")
    try:
        response = requests.post(
            'http://localhost:8000/api/books/author-search/',
            json={"author_name": "Jane Austen", "language": "en"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            image_url = data.get('author_image', '')
            print(f"✓ Status: {response.status_code}")
            print(f"✓ Author: {data.get('name', 'N/A')}")
            print(f"✓ Image URL: {image_url}")
            
            # Check image URL
            if not image_url:
                print("❌ No image URL returned")
            elif any(domain in image_url for domain in ['cdn.britannica.com', 'researchgate.net', 'poetryfoundation.org', 'images.unsplash.com', 'cdn.pixabay.com', 'images.pexels.com']):
                print("✅ SUCCESS: Using actual photo from Google Images search (reliable)")
            elif 'placehold.co' in image_url or 'dummyimage.com' in image_url:
                print("✅ SUCCESS: Using reliable placeholder image")
            elif 'wikimedia.org' in image_url or 'wikipedia.org' in image_url:
                print("❌ ERROR: Still using broken Wikimedia URL")
            elif 'unsplash.com' in image_url and '?' in image_url:
                print("❌ ERROR: Using Unsplash URL with query params (will 404)")
            else:
                print(f"⚠️  UNKNOWN: Using {image_url}")
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    print("\n=== Summary ===")
    print("✅ SUCCESS = Image URLs are now reliable and won't return 404")
    print("❌ ERROR = Image URLs still have issues")
    print("⚠️  UNKNOWN = Manual verification needed")
    print("\nIf you see SUCCESS messages, the fix is working!")


if __name__ == "__main__":
    test_api_endpoints()
