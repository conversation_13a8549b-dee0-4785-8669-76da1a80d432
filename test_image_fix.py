#!/usr/bin/env python
"""
Test script to specifically check if image URLs are fixed.
"""

import requests
import json

def test_category_images():
    """Test category search for image URLs."""
    url = "http://localhost:8000/api/category-search/"
    
    test_case = {"category_name": "entertainment", "language": "en"}
    
    print("=== Testing Category Image URL Fix ===")
    print(f"Testing: {test_case}")
    
    try:
        response = requests.post(url, json=test_case, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            image_url = data.get('image_url', '')
            
            print(f"✓ Status: {response.status_code}")
            print(f"✓ Category Name: {data.get('name', 'N/A')}")
            print(f"✓ Image URL: {image_url}")
            
            # Check if image URL is problematic
            if not image_url:
                print("✗ ERROR: No image URL returned!")
            elif 'wikimedia.org' in image_url or 'wikipedia.org' in image_url:
                print("✗ ERROR: Still returning Wikimedia URL!")
                print("  This will cause 'File not found' errors")
            elif 'images.unsplash.com' in image_url:
                print("✓ SUCCESS: Using Unsplash image (reliable)")
            elif 'www.biography.com' in image_url:
                print("✓ SUCCESS: Using Biography.com image (reliable)")
            elif 'via.placeholder.com' in image_url:
                print("✓ SUCCESS: Using placeholder image (reliable)")
            else:
                print(f"? UNKNOWN: Using image from: {image_url}")
                
            # Print full response for debugging
            print("\n--- Full Response ---")
            print(json.dumps(data, indent=2, ensure_ascii=False))
                
        else:
            print(f"✗ Error: {response.status_code}")
            print(f"✗ Response: {response.text}")
            
    except Exception as e:
        print(f"✗ Exception: {e}")


def test_author_images():
    """Test author search for image URLs."""
    url = "http://localhost:8000/api/author-search/"
    
    test_case = {"author_name": "Jane Austen", "language": "en"}
    
    print("\n=== Testing Author Image URL Fix ===")
    print(f"Testing: {test_case}")
    
    try:
        response = requests.post(url, json=test_case, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            image_url = data.get('author_image', '')
            
            print(f"✓ Status: {response.status_code}")
            print(f"✓ Author Name: {data.get('name', 'N/A')}")
            print(f"✓ Image URL: {image_url}")
            
            # Check if image URL is problematic
            if not image_url:
                print("✗ ERROR: No image URL returned!")
            elif 'wikimedia.org' in image_url or 'wikipedia.org' in image_url:
                print("✗ ERROR: Still returning Wikimedia URL!")
                print("  This will cause 'File not found' errors")
            elif 'images.unsplash.com' in image_url:
                print("✓ SUCCESS: Using Unsplash image (reliable)")
            elif 'www.biography.com' in image_url:
                print("✓ SUCCESS: Using Biography.com image (reliable)")
            elif 'via.placeholder.com' in image_url:
                print("✓ SUCCESS: Using placeholder image (reliable)")
            else:
                print(f"? UNKNOWN: Using image from: {image_url}")
                
            # Print partial response for debugging
            print("\n--- Key Response Fields ---")
            print(f"Name: {data.get('name')}")
            print(f"Image: {data.get('author_image')}")
            print(f"Bio length: {len(data.get('bio', '').split())} words")
                
        else:
            print(f"✗ Error: {response.status_code}")
            print(f"✗ Response: {response.text}")
            
    except Exception as e:
        print(f"✗ Exception: {e}")


def main():
    """Main test function."""
    print("Testing Image URL Fixes")
    print("========================")
    print("This test checks if the broken Wikimedia URLs have been fixed.")
    print()
    
    # Test both endpoints
    test_category_images()
    test_author_images()
    
    print("\n=== Summary ===")
    print("✓ SUCCESS means the image URL is from a reliable source")
    print("✗ ERROR means there are still issues to fix")
    print("? UNKNOWN means manual verification needed")


if __name__ == "__main__":
    main()
