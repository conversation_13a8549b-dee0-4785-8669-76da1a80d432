#!/usr/bin/env python
"""
Quick test for the website search endpoint.
"""

import requests
import json

def test_netflix():
    """Quick test with Netflix."""
    print("🎬 Testing Netflix Website Search")
    print("=" * 35)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    test_data = {
        "website_name": "Netflix",
        "language": "en"
    }
    
    print(f"URL: {url}")
    print(f"Data: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data, timeout=25)
        
        print(f"\nResponse Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Success!")
            print(f"\n📊 Basic Info:")
            print(f"   Name: {data.get('name', 'N/A')}")
            print(f"   Country: {data.get('country', 'N/A')}")
            print(f"   Founded: {data.get('founded', 'N/A')}")
            print(f"   Website: {data.get('website_url', 'N/A')}")
            
            # Category info
            category = data.get('category', {})
            if category:
                print(f"\n📂 Category:")
                print(f"   Name: {category.get('name', 'N/A')} {category.get('icon', '')}")
                print(f"   Wiki: {category.get('wikilink', 'N/A')}")
                
                desc = category.get('description', '')
                word_count = len(desc.split()) if desc else 0
                print(f"   Description: {word_count} words")
                print(f"   Text: {desc[:100]}...")
            
            # Brief description
            brief = data.get('brief_description', '')
            brief_words = len(brief.split()) if brief else 0
            print(f"\n📝 Brief Description ({brief_words} words):")
            print(f"   {brief}")
            
            # Comprehensive description
            comp = data.get('comprehensive_description', '')
            comp_words = len(comp.split()) if comp else 0
            print(f"\n📖 Comprehensive Description ({comp_words} words):")
            print(f"   {comp[:200]}...")
            
            # App links
            app_links = data.get('app_links', {})
            print(f"\n📱 App Links:")
            print(f"   Play Store: {app_links.get('playstore', 'N/A')}")
            print(f"   App Store: {app_links.get('appstore', 'N/A')}")
            
            # Social media
            social = data.get('social_media', {})
            print(f"\n📲 Social Media:")
            for platform, link in social.items():
                print(f"   {platform.title()}: {link or 'N/A'}")
            
            # Performance
            search_time = data.get('search_time', 0)
            print(f"\n⏱️  Search Time: {search_time:.1f} seconds")
            
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_google():
    """Quick test with Google."""
    print("\n🔍 Testing Google Website Search")
    print("=" * 32)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    test_data = {
        "website_name": "Google",
        "language": "en"
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=25)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Success!")
            print(f"Name: {data.get('name', 'N/A')}")
            print(f"Country: {data.get('country', 'N/A')}")
            
            category = data.get('category', {})
            if category:
                print(f"Category: {category.get('name', 'N/A')} {category.get('icon', '')}")
            
            brief = data.get('brief_description', '')
            print(f"Brief: {brief}")
            
        else:
            print(f"❌ Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_arabic():
    """Quick test with Arabic language."""
    print("\n🌍 Testing Arabic Language")
    print("=" * 28)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    test_data = {
        "website_name": "YouTube",
        "language": "ar"
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=25)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Success!")
            print(f"Name: {data.get('name', 'N/A')}")
            print(f"Country: {data.get('country', 'N/A')}")
            
            category = data.get('category', {})
            if category:
                print(f"Category: {category.get('name', 'N/A')} {category.get('icon', '')}")
            
            brief = data.get('brief_description', '')
            print(f"Brief (Arabic): {brief}")
            
        else:
            print(f"❌ Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🌐 QUICK WEBSITE SEARCH TESTS")
    print("=" * 40)
    
    # Test Netflix (detailed)
    test_netflix()
    
    # Test Google (basic)
    test_google()
    
    # Test Arabic
    test_arabic()
    
    print("\n✅ Quick tests completed!")
    print("If tests show ✅ Success, the website search API is working!")
