# Company/Stock Search API

## 🏢 **Purpose**

The `/api/books/company-search/` endpoint provides comprehensive information about companies and their stock data **without any database operations**. Perfect for getting detailed company profiles, financial data, and market information.

## 🚀 **Usage**

### Company Name Search
```bash
curl -X POST http://localhost:8000/api/books/company-search/ \
  -H "Content-Type: application/json" \
  -d '{
    "company_name": "Apple",
    "language": "en"
  }'
```

### Stock Ticker Search
```bash
curl -X POST http://localhost:8000/api/books/company-search/ \
  -H "Content-Type: application/json" \
  -d '{
    "company_name": "AAPL",
    "language": "en"
  }'
```

### Arabic Search
```bash
curl -X POST http://localhost:8000/api/books/company-search/ \
  -H "Content-Type: application/json" \
  -d '{
    "company_name": "Microsoft",
    "language": "ar"
  }'
```

## 📊 **Response Structure**

### Complete Response Example
```json
{
    "name": "Apple Inc.",
    "code": "AAPL",
    "company_email": "<EMAIL>",
    "web_url": "https://www.apple.com",
    "logo": "https://logo.clearbit.com/apple.com",
    "country_origin": "United States",
    "category": {
        "name": "Technology",
        "icon": "💻",
        "wikilink": "https://en.wikipedia.org/wiki/Technology",
        "description": "The technology industry encompasses companies that design, develop, and manufacture technological products and services. This sector includes hardware manufacturers, software developers, semiconductor companies, and digital service providers. Technology companies drive innovation across multiple industries, creating products that enhance productivity, communication, and entertainment. The sector is characterized by rapid innovation cycles, high research and development investments, and significant market valuations. Major technology companies often operate globally, providing platforms, devices, and services that connect billions of users worldwide, making this one of the most influential and valuable sectors in the modern economy."
    },
    "founded": "1976",
    "headquarters": "Cupertino, California, USA",
    "ceo": "Tim Cook",
    "employees": "164,000",
    "stock_data": {
        "last_52_weeks_low": 164.08,
        "last_52_weeks_high": 237.49,
        "market_cap": "3.2T",
        "yesterday_close": 210.02
    },
    "yesterday_data": {
        "Date": "2025-07-17T00:00:00-04:00",
        "Open": 210.57,
        "High": 211.80,
        "Low": 209.59,
        "Close": 210.02,
        "Volume": 48010700
    },
    "last_7_days_data": [
        {
            "Date": "2025-07-11T00:00:00-04:00",
            "Open": 210.57,
            "High": 212.13,
            "Low": 209.86,
            "Close": 211.16,
            "Volume": 39765800
        },
        {
            "Date": "2025-07-12T00:00:00-04:00",
            "Open": 211.20,
            "High": 213.45,
            "Low": 210.15,
            "Close": 212.80,
            "Volume": 41234500
        },
        {
            "Date": "2025-07-13T00:00:00-04:00",
            "Open": 212.85,
            "High": 214.20,
            "Low": 211.90,
            "Close": 213.15,
            "Volume": 38765200
        },
        {
            "Date": "2025-07-14T00:00:00-04:00",
            "Open": 209.93,
            "High": 210.91,
            "Low": 207.54,
            "Close": 208.62,
            "Volume": 38840100
        },
        {
            "Date": "2025-07-15T00:00:00-04:00",
            "Open": 209.22,
            "High": 211.89,
            "Low": 208.92,
            "Close": 209.11,
            "Volume": 42296300
        },
        {
            "Date": "2025-07-16T00:00:00-04:00",
            "Open": 210.30,
            "High": 212.40,
            "Low": 208.64,
            "Close": 210.16,
            "Volume": 47490500
        },
        {
            "Date": "2025-07-17T00:00:00-04:00",
            "Open": 210.57,
            "High": 211.80,
            "Low": 209.59,
            "Close": 210.02,
            "Volume": 48010700
        }
    ],
    "search_time": 12.4,
    "language": "en",
    "note": "Company information without database storage"
}
```

## ✨ **Features**

### 🏢 **Company Information**
- **Name**: Full company name
- **Stock Code**: Ticker symbol (AAPL, GOOGL, etc.)
- **Contact**: Company email and website
- **Logo**: Company logo URL
- **Location**: Country of origin and headquarters
- **Leadership**: Current CEO information
- **Size**: Employee count

### 📊 **Financial Data**
- **52-Week Range**: Highest and lowest stock prices in the last year
- **Market Cap**: Current market capitalization
- **Yesterday Close**: Previous trading day's closing price
- **Yesterday Complete**: Full OHLCV data for the previous trading day
- **7-Day History**: Complete OHLCV data for the last 7 trading days

### 🏷️ **Industry Classification**
- **Category**: Industry sector with icon and description
- **Wikipedia Link**: Reference to industry information
- **Structured Data**: Consistent categorization format

### 🌍 **Language Support**
- **English**: All content in English
- **Arabic**: All content in Arabic including company descriptions
- **Flexible Input**: Works with both company names and stock tickers

### 🚫 **No Database Operations**
- Pure search results without any storage
- No models, serializers, or CRUD operations
- Stateless API responses

## 📝 **Request Parameters**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `company_name` | string | Yes | Company name or stock ticker |
| `language` | string | No | Language preference ("en" or "ar", default: "en") |

## 📊 **Response Fields**

| Field | Type | Description |
|-------|------|-------------|
| `name` | string | Full company name |
| `code` | string | Stock ticker symbol |
| `company_email` | string | Contact email |
| `web_url` | string | Official website |
| `logo` | string | Company logo URL |
| `country_origin` | string | Country of origin |
| `category` | object | Industry category with details |
| `founded` | string | Year founded |
| `headquarters` | string | Headquarters location |
| `ceo` | string | Current CEO |
| `employees` | string | Employee count |
| `stock_data` | object | Financial metrics |
| `last_7_days_data` | array | 7 days of trading data |
| `search_time` | number | API response time |
| `language` | string | Response language |

## 📈 **Stock Data Fields**

| Field | Description |
|-------|-------------|
| `last_52_weeks_low` | Lowest price in the last 52 weeks |
| `last_52_weeks_high` | Highest price in the last 52 weeks |
| `market_cap` | Current market capitalization |
| `yesterday_close` | Previous trading day's closing price |

## 📊 **7-Day Data Format**

Each day includes:
- **Date**: Trading date with timezone
- **Open**: Opening price
- **High**: Highest price of the day
- **Low**: Lowest price of the day
- **Close**: Closing price
- **Volume**: Number of shares traded

## 🎯 **Supported Companies**

### Works with Any Public Company
- **By Name**: Apple, Google, Microsoft, Tesla, Amazon
- **By Ticker**: AAPL, GOOGL, MSFT, TSLA, AMZN
- **International**: Works with global companies
- **Flexible**: Handles various input formats

### Popular Examples
- **Technology**: Apple (AAPL), Google (GOOGL), Microsoft (MSFT)
- **E-commerce**: Amazon (AMZN), eBay (EBAY)
- **Automotive**: Tesla (TSLA), Ford (F)
- **Entertainment**: Netflix (NFLX), Disney (DIS)
- **Finance**: JPMorgan (JPM), Goldman Sachs (GS)

## 🧪 **Testing**

### Test Scripts
```bash
# Quick test
python test_quick_company.py

# Comprehensive test
python test_company_search.py
```

### Manual Testing
```bash
# Test Apple
curl -X POST http://localhost:8000/api/books/company-search/ \
  -H "Content-Type: application/json" \
  -d '{"company_name": "Apple", "language": "en"}'

# Test with stock ticker
curl -X POST http://localhost:8000/api/books/company-search/ \
  -H "Content-Type: application/json" \
  -d '{"company_name": "GOOGL", "language": "en"}'
```

## 🚨 **Error Handling**

### Error Responses
```json
// Empty company name
{
    "error": "company_name is required"
}

// Invalid language
{
    "error": "Language must be \"en\" or \"ar\""
}
```

## 📈 **Performance**

### Expected Response Times
- **Popular companies**: < 15 seconds
- **Complex queries**: < 20 seconds
- **Fallback responses**: < 8 seconds

### Performance Features
- Zero database operations
- Direct JSON responses
- Efficient LLM prompts
- Smart fallback system

## 🎯 **Use Cases**

### Perfect For:
- ✅ Financial applications and dashboards
- ✅ Investment research platforms
- ✅ Company profile pages
- ✅ Stock market analysis tools
- ✅ Business intelligence applications
- ✅ Portfolio management systems

### Example Applications:
- Display company information on trading platforms
- Create company comparison tools
- Build investment analysis dashboards
- Generate financial reports
- Create stock screening applications

## 🔧 **Integration Examples**

### JavaScript/React
```javascript
const searchCompany = async (companyName, language = 'en') => {
    const response = await fetch('/api/books/company-search/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ company_name: companyName, language })
    });
    
    if (response.ok) {
        const companyData = await response.json();
        console.log(`${companyData.name} (${companyData.code})`);
        console.log(`Market Cap: ${companyData.stock_data.market_cap}`);
        return companyData;
    }
};
```

### Python
```python
import requests

def search_company(company_name, language='en'):
    url = 'http://localhost:8000/api/books/company-search/'
    data = {'company_name': company_name, 'language': language}
    
    response = requests.post(url, json=data)
    if response.status_code == 200:
        company_data = response.json()
        print(f"{company_data['name']} ({company_data['code']})")
        print(f"CEO: {company_data['ceo']}")
        print(f"Market Cap: {company_data['stock_data']['market_cap']}")
        return company_data
    
    return None
```

## 🎉 **Summary**

The Company Search API provides:
- 🏢 **Complete company profiles** with logos and contact information
- 📊 **Real-time stock data** with 52-week ranges and market cap
- 📈 **Historical data** with 7 days of trading information
- 🏷️ **Industry classification** with structured categories
- 👨‍💼 **Leadership information** including CEO and employee data
- 🌍 **Full language support** for Arabic and English
- 🚫 **No database complexity** - pure search results
- ⚡ **Fast performance** with comprehensive data

Perfect for any financial or business application that needs comprehensive company and stock information without the overhead of database management!
