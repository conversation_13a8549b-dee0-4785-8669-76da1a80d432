#!/usr/bin/env python
"""
Test script specifically for Arabic book description analysis.
"""

import os
import sys
import django
import json
import requests
import time

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_api_project.settings')
django.setup()

def count_words(text):
    """Count words in a text string."""
    if not text:
        return 0
    return len(text.split())

def is_arabic_text(text):
    """Check if text contains Arabic characters."""
    if not text:
        return False
    arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
    return arabic_chars > len(text) * 0.3

def test_arabic_descriptions():
    """Test Arabic book description analysis."""
    print("🌍 Arabic Book Description Analysis Test")
    print("=" * 50)
    
    # API endpoint (note the trailing slash)
    url = "http://localhost:8000/api/books/analyze-description/"
    
    # Arabic test cases
    test_cases = [
        {
            "name": "ألف ليلة وليلة - حكايات شعبية",
            "description": "مجموعة من الحكايات الشعبية العربية الشهيرة التي تحكي قصص الحب والمغامرة والحكمة. تتضمن هذه القصص شخصيات مثل علاء الدين وعلي بابا والأربعين حرامي وشهرزاد. تستكشف هذه الحكايات موضوعات العدالة والشجاعة والذكاء والخير والشر. هذه القصص تعكس الثقافة العربية الغنية وتقدم دروساً أخلاقية قيمة للقراء من جميع الأعمار وتنقل التراث الشعبي عبر الأجيال.",
            "expected_categories": ["أدب شعبي", "حكايات", "تراث عربي"]
        },
        {
            "name": "رواية عربية معاصرة",
            "description": "رواية تحكي قصة شاب عربي يعيش في مدينة كبيرة ويواجه تحديات الحياة المعاصرة. يستكشف الكاتب من خلال هذه الرواية موضوعات الهوية والانتماء والصراع بين التقاليد والحداثة. تتناول الرواية قضايا اجتماعية مهمة مثل البطالة والهجرة والحب والزواج. الأسلوب السردي يمزج بين الواقعية والرمزية ويقدم صورة صادقة عن المجتمع العربي المعاصر وتحدياته.",
            "expected_categories": ["رواية عربية", "أدب معاصر", "قضايا اجتماعية"]
        },
        {
            "name": "كتاب تاريخ إسلامي",
            "description": "دراسة شاملة للتاريخ الإسلامي من بداية الدعوة النبوية حتى العصر العباسي. يتناول الكتاب الأحداث التاريخية المهمة والشخصيات المؤثرة في تشكيل الحضارة الإسلامية. يركز المؤلف على الفتوحات الإسلامية وانتشار الإسلام في مختلف البلدان والقارات. كما يستعرض التطورات العلمية والثقافية والفنية في العالم الإسلامي ودور العلماء المسلمين في النهضة العلمية والحضارية.",
            "expected_categories": ["تاريخ إسلامي", "حضارة إسلامية", "دراسات تاريخية"]
        },
        {
            "name": "ديوان شعر عربي كلاسيكي",
            "description": "مجموعة من القصائد العربية الكلاسيكية التي تعبر عن مشاعر الحب والفراق والحنين إلى الوطن. يتميز هذا الديوان بجمال اللغة وقوة التعبير وعمق المعاني. تتنوع القصائد بين الغزل والمدح والرثاء والوصف. يعكس الشعر التراث الأدبي العربي الأصيل ويظهر مهارة الشاعر في استخدام البحور الشعرية والقوافي. هذا العمل يساهم في الحفاظ على التراث الشعري العربي ونقله للأجيال الجديدة.",
            "expected_categories": ["شعر عربي", "أدب كلاسيكي", "تراث أدبي"]
        },
        {
            "name": "كتاب فلسفة إسلامية",
            "description": "بحث عميق في الفلسفة الإسلامية وأهم مفكريها مثل ابن سينا والغزالي وابن رشد. يتناول الكتاب القضايا الفلسفية الكبرى مثل الوجود والمعرفة والأخلاق من منظور إسلامي. يستكشف المؤلف العلاقة بين العقل والنقل وكيفية التوفيق بين الفلسفة والدين. كما يناقش تأثير الفلسفة الإسلامية على الفكر الأوروبي في العصور الوسطى. هذا العمل يقدم فهماً عميقاً للتراث الفكري الإسلامي ومساهمته في الحضارة الإنسانية.",
            "expected_categories": ["فلسفة إسلامية", "فكر إسلامي", "تراث فكري"]
        }
    ]
    
    total_tests = len(test_cases)
    passed_tests = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📚 اختبار {i}/{total_tests}: {test_case['name']}")
        print("-" * 40)
        
        start_time = time.time()
        
        try:
            # Prepare request data
            request_data = {
                "description": test_case["description"],
                "language": "ar"
            }
            
            response = requests.post(url, json=request_data, timeout=30)
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"⏱️  وقت الاستجابة: {response_time:.1f} ثانية")
            
            if response.status_code == 200:
                data = response.json()
                
                print(f"✅ الحالة: {response.status_code}")
                print(f"📊 الفئات الموجودة: {data.get('total_categories', 0)}")
                
                analysis_summary = data.get('analysis_summary', '')
                if analysis_summary:
                    print(f"🔍 ملخص التحليل: {analysis_summary[:100]}...")
                
                categories = data.get('categories', [])
                
                word_count_correct = True
                language_correct = True
                structure_correct = True
                
                # Validate each category
                for j, category in enumerate(categories, 1):
                    name = category.get('name', '')
                    icon = category.get('icon', '')
                    wikilink = category.get('wikilink', '')
                    description = category.get('description', '')
                    
                    print(f"\n   📂 الفئة {j}:")
                    print(f"      الاسم: {name}")
                    print(f"      الرمز: {icon}")
                    print(f"      رابط ويكي: {wikilink}")
                    
                    # Check if name is in Arabic
                    name_arabic = is_arabic_text(name)
                    print(f"      اسم عربي: {'✅' if name_arabic else '❌'}")
                    
                    # Check word count (should be around 100 words)
                    word_count = count_words(description)
                    word_status = "✅" if 90 <= word_count <= 110 else "❌"
                    print(f"      الوصف: {word_count} كلمة {word_status}")
                    
                    # Check if description is in Arabic
                    desc_arabic = is_arabic_text(description)
                    print(f"      وصف عربي: {'✅' if desc_arabic else '❌'}")
                    
                    # Check Wikipedia link
                    wiki_arabic = 'ar.wikipedia.org' in wikilink
                    print(f"      رابط ويكي عربي: {'✅' if wiki_arabic else '❌'}")
                    
                    # Show description preview
                    print(f"      نص الوصف: {description[:100]}...")
                    
                    # Update validation flags
                    if not (90 <= word_count <= 110):
                        word_count_correct = False
                    
                    if not (name_arabic and desc_arabic):
                        language_correct = False
                    
                    if not (name and icon and wikilink and description):
                        structure_correct = False
                
                # Check analysis summary language
                if analysis_summary:
                    summary_arabic = is_arabic_text(analysis_summary)
                    print(f"\n   📝 ملخص التحليل عربي: {'✅' if summary_arabic else '❌'}")
                    if not summary_arabic:
                        language_correct = False
                
                # Overall assessment
                print(f"\n🎯 التقييم:")
                print(f"   وقت الاستجابة: {'✅ سريع' if response_time < 15 else '❌ بطيء'} ({response_time:.1f}ث)")
                print(f"   عدد الكلمات: {'✅ صحيح' if word_count_correct else '❌ خطأ'}")
                print(f"   اللغة العربية: {'✅ صحيح' if language_correct else '❌ خطأ'}")
                print(f"   البنية: {'✅ مكتملة' if structure_correct else '❌ ناقصة'}")
                print(f"   عدد الفئات: {len(categories)}")
                
                if (response_time < 15 and word_count_correct and 
                    language_correct and structure_correct and len(categories) > 0):
                    passed_tests += 1
                    print(f"   النتيجة العامة: ✅ نجح")
                else:
                    print(f"   النتيجة العامة: ❌ فشل")
                
            else:
                print(f"❌ خطأ: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"تفاصيل الخطأ: {error_data}")
                except:
                    print(f"الاستجابة: {response.text}")
                
        except requests.exceptions.Timeout:
            print("❌ انتهت مهلة الطلب (> 30 ثانية)")
        except requests.exceptions.RequestException as e:
            print(f"❌ خطأ في الطلب: {e}")
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {e}")
        
        # Add delay between tests to avoid rate limiting
        if i < total_tests:
            print("⏳ انتظار 8 ثوان قبل الاختبار التالي...")
            time.sleep(8)
    
    # Final summary
    print("\n" + "=" * 50)
    print("📋 ملخص اختبار تحليل الوصف العربي")
    print("=" * 50)
    print(f"الاختبارات الناجحة: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ تحليل الوصف العربي يعمل بشكل مثالي")
        print("📝 عدد الكلمات صحيح (90-110 كلمة)")
        print("🌍 معالجة اللغة العربية دقيقة")
        print("⚡ أوقات الاستجابة مقبولة")
    elif passed_tests > 0:
        print("⚠️ نجاح جزئي")
        print("🔧 بعض الاختبارات نجحت، الواجهة تعمل لكن قد تحتاج تحسينات")
    else:
        print("❌ جميع الاختبارات فشلت")
        print("🔧 الواجهة تحتاج إصلاحات كبيرة")
    
    return passed_tests == total_tests

def test_direct_arabic_llm():
    """Test the LLM service directly with Arabic input."""
    print("\n🤖 اختبار خدمة الذكاء الاصطناعي المباشر")
    print("=" * 40)
    
    try:
        from books.services.llm_service import LLMService
        
        llm_service = LLMService()
        
        print("اختبار تحليل وصف عربي...")
        start_time = time.time()
        
        arabic_description = """
        رواية تاريخية تحكي قصة الحضارة الإسلامية في الأندلس وازدهار العلوم والفنون. 
        تتناول الرواية حياة العلماء والفلاسفة والشعراء في قرطبة وإشبيلية. 
        يستكشف المؤلف التفاعل الثقافي بين المسلمين والمسيحيين واليهود في الأندلس.
        """
        
        result = llm_service.analyze_description_for_categories(arabic_description.strip(), "ar")
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"⏱️ الوقت: {response_time:.1f} ثانية")
        
        categories = result.get('categories', [])
        analysis_summary = result.get('analysis_summary', '')
        
        print(f"📊 النتائج:")
        print(f"   الفئات: {len(categories)} {'✅' if categories else '❌'}")
        print(f"   ملخص التحليل: {'✅' if analysis_summary else '❌'}")
        
        if analysis_summary:
            summary_arabic = is_arabic_text(analysis_summary)
            print(f"   ملخص عربي: {'✅' if summary_arabic else '❌'}")
            print(f"   الملخص: {analysis_summary[:100]}...")
        
        for i, cat in enumerate(categories, 1):
            name = cat.get('name', '')
            desc = cat.get('description', '')
            
            name_arabic = is_arabic_text(name)
            desc_arabic = is_arabic_text(desc)
            word_count = count_words(desc)
            
            print(f"   الفئة {i}: {name}")
            print(f"     اسم عربي: {'✅' if name_arabic else '❌'}")
            print(f"     وصف عربي: {'✅' if desc_arabic else '❌'}")
            print(f"     عدد الكلمات: {word_count} {'✅' if 90 <= word_count <= 110 else '❌'}")
        
        success = (categories and analysis_summary and 
                  response_time < 10 and 
                  all(is_arabic_text(cat.get('name', '')) for cat in categories))
        
        print(f"   النتيجة: {'✅ نجح' if success else '❌ فشل'}")
        
        return success
        
    except Exception as e:
        print(f"❌ فشل اختبار الذكاء الاصطناعي: {e}")
        return False

if __name__ == "__main__":
    print("🌍 مجموعة اختبارات تحليل الوصف العربي")
    print("=" * 60)
    
    # Test LLM service first
    llm_passed = test_direct_arabic_llm()
    
    # Test full API
    api_passed = test_arabic_descriptions()
    
    print(f"\n🏁 النتيجة النهائية")
    print("=" * 25)
    if llm_passed and api_passed:
        print("✅ نجح اختبار تحليل الوصف العربي!")
        print("📚 الواجهة تحلل الأوصاف العربية بشكل صحيح")
        print("🏷️ ترجع معلومات فئات منظمة")
        print("📝 تحافظ على عدد الكلمات الصحيح (90-110 كلمة)")
        print("🌍 تدعم اللغة العربية بالكامل")
    elif api_passed:
        print("⚠️ الواجهة تعمل لكن مشاكل في الذكاء الاصطناعي")
        print("🔧 خدمة الذكاء الاصطناعي تحتاج انتباه")
    elif llm_passed:
        print("⚠️ الذكاء الاصطناعي يعمل لكن مشاكل في الواجهة")
        print("🔧 تكامل الواجهة يحتاج انتباه")
    else:
        print("❌ مشاكل كبيرة")
        print("🔧 كل من الذكاء الاصطناعي والواجهة يحتاجان إصلاح")
    
    sys.exit(0 if api_passed else 1)
