#!/usr/bin/env python
"""
Test Google Images search functionality.
"""

import requests
from urllib.parse import quote_plus
import re

def is_valid_google_image_url(url: str) -> bool:
    """
    Validate if a Google Images URL is suitable for use.
    """
    if not url or not isinstance(url, str):
        return False
    
    url_lower = url.lower()
    
    # Reject unwanted domains
    blocked_domains = [
        'wikimedia.org',
        'wikipedia.org',
        'google.com',
        'googleusercontent.com',
        'gstatic.com',
        'encrypted-tbn',  # Google's encrypted thumbnails
    ]
    
    if any(domain in url_lower for domain in blocked_domains):
        return False
    
    # Must be a direct image URL
    if not url_lower.endswith(('.jpg', '.jpeg', '.png', '.webp', '.gif')):
        return False
    
    # Must be from a reasonable domain
    if len(url) > 500:  # Too long URLs are often problematic
        return False
    
    return True


def search_google_images(query: str, image_type: str = "general") -> str:
    """
    Search Google Images for free using web scraping (no API key required).
    Returns the first valid image URL found.
    """
    try:
        print(f"Searching Google Images for: {query} ({image_type})")
        
        # Prepare search query
        search_query = f"{query} {image_type}" if image_type != "general" else query
        encoded_query = quote_plus(search_query)
        
        # Google Images search URL
        search_url = f"https://www.google.com/search?q={encoded_query}&tbm=isch&safe=active"
        
        # Headers to mimic a real browser
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        # Make request to Google Images
        response = requests.get(search_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            print(f"Google search successful, status: {response.status_code}")
            
            # Extract image URLs from the response
            # Look for image URLs in the HTML
            image_pattern = r'"(https?://[^"]*\.(?:jpg|jpeg|png|webp|gif))"'
            matches = re.findall(image_pattern, response.text, re.IGNORECASE)
            
            print(f"Found {len(matches)} potential image URLs")
            
            # Filter out unwanted domains and find a good image
            valid_count = 0
            for url in matches:
                if is_valid_google_image_url(url):
                    valid_count += 1
                    print(f"Valid image #{valid_count}: {url}")
                    if valid_count == 1:  # Return the first valid one
                        return url
                else:
                    print(f"Rejected: {url[:100]}...")
            
            print("No valid images found")
        else:
            print(f"Google search failed with status: {response.status_code}")
        
        return ""
        
    except Exception as e:
        print(f"Google Images search error: {e}")
        return ""


def test_google_search():
    """Test the Google Images search."""
    test_queries = [
        ("Jane Austen", "author"),
        ("Shakespeare", "author"),
        ("entertainment", "category"),
        ("technology", "category"),
    ]
    
    for query, image_type in test_queries:
        print(f"\n{'='*50}")
        print(f"Testing: {query} ({image_type})")
        print('='*50)
        
        result = search_google_images(query, image_type)
        
        if result:
            print(f"✅ SUCCESS: Found image URL")
            print(f"URL: {result}")
        else:
            print(f"❌ FAILED: No valid image found")


if __name__ == "__main__":
    test_google_search()
