#!/usr/bin/env python
"""
Quick test for both English and Arabic description analysis.
"""

import os
import sys
import django
import json
import requests
import time

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_api_project.settings')
django.setup()

def count_words(text):
    """Count words in a text string."""
    if not text:
        return 0
    return len(text.split())

def is_arabic_text(text):
    """Check if text contains Arabic characters."""
    if not text:
        return False
    arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
    return arabic_chars > len(text) * 0.3

def test_both_languages():
    """Test description analysis for both languages."""
    print("🌍 English & Arabic Description Analysis Test")
    print("=" * 55)
    
    url = "http://localhost:8000/api/books/analyze-description/"
    
    test_cases = [
        {
            "name": "English Fiction",
            "data": {
                "description": "A gripping tale of love, betrayal, and redemption set in Victorian England. The story follows <PERSON>, a young woman who must navigate the complex social hierarchies of her time while pursuing her dreams of independence. Through her journey, she encounters mysterious characters, faces moral dilemmas, and discovers the true meaning of courage and sacrifice.",
                "language": "en"
            },
            "expected_lang": "en"
        },
        {
            "name": "Arabic Literature",
            "data": {
                "description": "رواية تحكي قصة شاب عربي يسافر عبر الصحراء بحثاً عن كنز مفقود. خلال رحلته يلتقي بشخصيات مختلفة تعلمه دروساً مهمة عن الحياة والحب والصداقة. تستكشف الرواية موضوعات الهوية والانتماء والبحث عن المعنى في الحياة. الأسلوب السردي يمزج بين الواقع والخيال ويقدم رؤية عميقة للثقافة العربية.",
                "language": "ar"
            },
            "expected_lang": "ar"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📚 Test {i}: {test_case['name']}")
        print("-" * 30)
        
        start_time = time.time()
        
        try:
            response = requests.post(url, json=test_case['data'], timeout=25)
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"⏱️  Response Time: {response_time:.1f}s")
            
            if response.status_code == 200:
                data = response.json()
                categories = data.get('categories', [])
                analysis_summary = data.get('analysis_summary', '')
                
                print(f"✅ Status: Success")
                print(f"📊 Categories: {len(categories)}")
                print(f"📝 Summary: {analysis_summary[:50]}...")
                
                # Validate language and structure
                lang_correct = True
                word_count_correct = True
                structure_complete = True
                
                expected_lang = test_case['expected_lang']
                
                # Check analysis summary language
                if analysis_summary:
                    if expected_lang == 'ar':
                        if not is_arabic_text(analysis_summary):
                            lang_correct = False
                            print(f"   ❌ Summary not in Arabic")
                    else:
                        if is_arabic_text(analysis_summary):
                            lang_correct = False
                            print(f"   ❌ Summary not in English")
                
                # Check categories
                for j, cat in enumerate(categories, 1):
                    name = cat.get('name', '')
                    desc = cat.get('description', '')
                    icon = cat.get('icon', '')
                    wikilink = cat.get('wikilink', '')
                    
                    print(f"\n   📂 Category {j}: {name} {icon}")
                    
                    # Check structure
                    if not all([name, desc, icon, wikilink]):
                        structure_complete = False
                        print(f"      ❌ Missing fields")
                    
                    # Check word count
                    word_count = count_words(desc)
                    if not (90 <= word_count <= 110):
                        word_count_correct = False
                        print(f"      ❌ Word count: {word_count} (expected 90-110)")
                    else:
                        print(f"      ✅ Word count: {word_count}")
                    
                    # Check language
                    if expected_lang == 'ar':
                        name_ok = is_arabic_text(name)
                        desc_ok = is_arabic_text(desc)
                        wiki_ok = 'ar.wikipedia.org' in wikilink
                        
                        if not (name_ok and desc_ok):
                            lang_correct = False
                            print(f"      ❌ Language: Name Arabic: {name_ok}, Desc Arabic: {desc_ok}")
                        else:
                            print(f"      ✅ Language: Arabic")
                        
                        if not wiki_ok:
                            print(f"      ⚠️  Wiki link not Arabic")
                    else:
                        name_ok = not is_arabic_text(name)
                        desc_ok = not is_arabic_text(desc)
                        wiki_ok = 'en.wikipedia.org' in wikilink
                        
                        if not (name_ok and desc_ok):
                            lang_correct = False
                            print(f"      ❌ Language: Name English: {name_ok}, Desc English: {desc_ok}")
                        else:
                            print(f"      ✅ Language: English")
                        
                        if not wiki_ok:
                            print(f"      ⚠️  Wiki link not English")
                
                # Overall assessment
                success = (response_time < 20 and lang_correct and 
                          word_count_correct and structure_complete and len(categories) > 0)
                
                print(f"\n🎯 Assessment:")
                print(f"   Speed: {'✅' if response_time < 20 else '❌'}")
                print(f"   Language: {'✅' if lang_correct else '❌'}")
                print(f"   Word Count: {'✅' if word_count_correct else '❌'}")
                print(f"   Structure: {'✅' if structure_complete else '❌'}")
                print(f"   Result: {'✅ PASSED' if success else '❌ FAILED'}")
                
                results.append(success)
                
            else:
                print(f"❌ Error: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            results.append(False)
        
        # Delay between tests
        if i < len(test_cases):
            print("⏳ Waiting 8s...")
            time.sleep(8)
    
    # Final summary
    print("\n" + "=" * 55)
    print("📋 BILINGUAL TEST SUMMARY")
    print("=" * 55)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Both English and Arabic work perfectly")
        print("📝 Word counts are correct (90-110)")
        print("🌍 Language-specific content is accurate")
        print("⚡ Response times are acceptable")
    elif passed > 0:
        print("⚠️  PARTIAL SUCCESS")
        print("🔧 Some language support works")
    else:
        print("❌ ALL TESTS FAILED")
        print("🔧 Both languages need fixing")
    
    return passed == total

def quick_api_test():
    """Quick test of the API endpoint."""
    print("\n🚀 Quick API Test")
    print("=" * 20)
    
    url = "http://localhost:8000/api/books/analyze-description/"

    # Simple English test
    print("Testing English...")
    try:
        response = requests.post(url, json={
            "description": "A classic novel about love and society in 19th century England.",
            "language": "en"
        }, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            categories = data.get('categories', [])
            print(f"✅ English: {len(categories)} categories")
        else:
            print(f"❌ English failed: {response.status_code}")
    except Exception as e:
        print(f"❌ English error: {e}")
    
    time.sleep(3)
    
    # Simple Arabic test
    print("Testing Arabic...")
    try:
        response = requests.post(url, json={
            "description": "رواية كلاسيكية تحكي قصة الحب والمجتمع في القرن التاسع عشر.",
            "language": "ar"
        }, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            categories = data.get('categories', [])
            print(f"✅ Arabic: {len(categories)} categories")
        else:
            print(f"❌ Arabic failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Arabic error: {e}")

if __name__ == "__main__":
    print("🌍 BILINGUAL DESCRIPTION ANALYSIS TEST")
    print("=" * 60)
    
    # Quick test first
    quick_api_test()
    
    # Full test
    success = test_both_languages()
    
    print(f"\n🏁 FINAL RESULT")
    print("=" * 25)
    if success:
        print("✅ BILINGUAL API SUCCESS!")
        print("🌍 Both English and Arabic work perfectly")
        print("📚 Description analysis is accurate")
        print("🏷️  Category extraction works well")
    else:
        print("⚠️  NEEDS IMPROVEMENT")
        print("🔧 Check language-specific issues")
    
    sys.exit(0 if success else 1)
