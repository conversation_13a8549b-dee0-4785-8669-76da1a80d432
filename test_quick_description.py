#!/usr/bin/env python
"""
Quick test for the description analysis API with proper URL.
"""

import requests
import json

def test_quick_description():
    """Quick test of the description analysis API."""
    print("🚀 Quick Description Analysis Test")
    print("=" * 40)
    
    # Correct URL with trailing slash
    url = "http://localhost:8000/api/books/analyze-description/"
    
    # Test data
    test_data = {
        "description": "A classic novel about love and society in 19th century England. The story explores themes of pride, prejudice, and social class through the eyes of <PERSON>.",
        "language": "en"
    }
    
    print("Testing English description...")
    print(f"URL: {url}")
    print(f"Data: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data, timeout=20)
        
        print(f"\nResponse Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            categories = data.get('categories', [])
            
            print(f"✅ Success!")
            print(f"Categories found: {len(categories)}")
            
            for i, cat in enumerate(categories, 1):
                name = cat.get('name', 'N/A')
                icon = cat.get('icon', '')
                print(f"  {i}. {name} {icon}")
            
            print(f"Analysis summary: {data.get('analysis_summary', 'N/A')}")
            
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_arabic_description():
    """Quick test with Arabic description."""
    print("\n🌍 Arabic Description Test")
    print("=" * 30)
    
    url = "http://localhost:8000/api/books/analyze-description/"
    
    test_data = {
        "description": "رواية كلاسيكية تحكي قصة الحب والمجتمع في القرن التاسع عشر. تستكشف القصة موضوعات الكبرياء والتحامل والطبقات الاجتماعية.",
        "language": "ar"
    }
    
    print("Testing Arabic description...")
    
    try:
        response = requests.post(url, json=test_data, timeout=20)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            categories = data.get('categories', [])
            
            print(f"✅ Success!")
            print(f"Categories found: {len(categories)}")
            
            for i, cat in enumerate(categories, 1):
                name = cat.get('name', 'N/A')
                icon = cat.get('icon', '')
                print(f"  {i}. {name} {icon}")
            
            print(f"Analysis summary: {data.get('analysis_summary', 'N/A')}")
            
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_error_case():
    """Test error handling."""
    print("\n🚨 Error Handling Test")
    print("=" * 25)
    
    url = "http://localhost:8000/api/books/analyze-description/"
    
    # Test with empty description
    test_data = {
        "description": "",
        "language": "en"
    }
    
    print("Testing empty description...")
    
    try:
        response = requests.post(url, json=test_data, timeout=10)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 400:
            print("✅ Correct error handling!")
            data = response.json()
            print(f"Error message: {data.get('error', 'N/A')}")
        else:
            print(f"❌ Unexpected status: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    print("🧪 QUICK DESCRIPTION ANALYSIS TESTS")
    print("=" * 50)
    
    # Test English
    test_quick_description()
    
    # Test Arabic
    test_arabic_description()
    
    # Test error handling
    test_error_case()
    
    print("\n✅ Quick tests completed!")
    print("If all tests show ✅ Success, the API is working correctly.")
