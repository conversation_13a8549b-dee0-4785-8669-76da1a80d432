#!/usr/bin/env python
"""
Test to verify word count fix - no more "additional additional additional".
"""

import requests
import json

def test_netflix_word_count():
    """Test Netflix for proper word counts without repetitive words."""
    print("📝 Testing Netflix Word Count Fix")
    print("=" * 35)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    test_data = {
        "website_name": "Netflix",
        "language": "en"
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=25)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Success!")
            
            # Check category description
            category = data.get('category', {})
            cat_desc = category.get('description', '')
            cat_words = len(cat_desc.split()) if cat_desc else 0
            
            print(f"\n📂 Category Description:")
            print(f"   Word Count: {cat_words}")
            print(f"   Target: 90 words")
            print(f"   Status: {'✅ GOOD' if 85 <= cat_words <= 95 else '❌ OFF'}")
            
            # Check for repetitive words
            repetitive_words = cat_desc.count('additional')
            print(f"   'Additional' count: {repetitive_words} {'❌ TOO MANY' if repetitive_words > 3 else '✅ OK'}")
            
            print(f"   Text: {cat_desc[:150]}...")
            
            # Check brief description
            brief_desc = data.get('brief_description', '')
            brief_words = len(brief_desc.split()) if brief_desc else 0
            
            print(f"\n📝 Brief Description:")
            print(f"   Word Count: {brief_words}")
            print(f"   Target: 40 words")
            print(f"   Status: {'✅ GOOD' if 35 <= brief_words <= 45 else '❌ OFF'}")
            print(f"   Text: {brief_desc}")
            
            # Check comprehensive description
            comp_desc = data.get('comprehensive_description', '')
            comp_words = len(comp_desc.split()) if comp_desc else 0
            
            print(f"\n📖 Comprehensive Description:")
            print(f"   Word Count: {comp_words}")
            print(f"   Target: 200 words")
            print(f"   Status: {'✅ GOOD' if 190 <= comp_words <= 210 else '❌ OFF'}")
            
            # Check for repetitive words in comprehensive
            comp_repetitive = comp_desc.count('additional')
            print(f"   'Additional' count: {comp_repetitive} {'❌ TOO MANY' if comp_repetitive > 5 else '✅ OK'}")
            
            print(f"   Text: {comp_desc[:200]}...")
            
            # Overall assessment
            word_counts_good = (
                85 <= cat_words <= 95 and
                35 <= brief_words <= 45 and
                190 <= comp_words <= 210
            )
            
            no_repetition = repetitive_words <= 3 and comp_repetitive <= 5
            
            print(f"\n🎯 Overall Assessment:")
            print(f"   Word Counts: {'✅ GOOD' if word_counts_good else '❌ OFF'}")
            print(f"   No Repetition: {'✅ GOOD' if no_repetition else '❌ TOO REPETITIVE'}")
            print(f"   Category: {category.get('name', 'N/A')} {category.get('icon', '')}")
            
            success = word_counts_good and no_repetition
            print(f"   Result: {'✅ EXCELLENT' if success else '❌ NEEDS WORK'}")
            
            return success
            
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_google_word_count():
    """Test Google for word counts."""
    print("\n🔍 Testing Google Word Count")
    print("=" * 28)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    test_data = {
        "website_name": "Google",
        "language": "en"
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=20)
        
        if response.status_code == 200:
            data = response.json()
            
            category = data.get('category', {})
            cat_desc = category.get('description', '')
            cat_words = len(cat_desc.split()) if cat_desc else 0
            
            brief_desc = data.get('brief_description', '')
            brief_words = len(brief_desc.split()) if brief_desc else 0
            
            print(f"✅ Success!")
            print(f"Category: {category.get('name', 'N/A')} {category.get('icon', '')}")
            print(f"Category Desc: {cat_words} words")
            print(f"Brief Desc: {brief_words} words")
            
            # Check for repetitive words
            repetitive = cat_desc.count('additional') + brief_desc.count('additional')
            print(f"Repetitive words: {repetitive} {'✅ OK' if repetitive <= 3 else '❌ TOO MANY'}")
            
            return repetitive <= 3
            
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_arabic_word_count():
    """Test Arabic word counts."""
    print("\n🌍 Testing Arabic Word Count")
    print("=" * 28)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    test_data = {
        "website_name": "YouTube",
        "language": "ar"
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=20)
        
        if response.status_code == 200:
            data = response.json()
            
            category = data.get('category', {})
            cat_desc = category.get('description', '')
            cat_words = len(cat_desc.split()) if cat_desc else 0
            
            brief_desc = data.get('brief_description', '')
            brief_words = len(brief_desc.split()) if brief_desc else 0
            
            print(f"✅ Success!")
            print(f"Category: {category.get('name', 'N/A')} {category.get('icon', '')}")
            print(f"Category Desc: {cat_words} words")
            print(f"Brief Desc: {brief_words} words")
            
            # Check for repetitive Arabic words
            repetitive = cat_desc.count('إضافي') + brief_desc.count('إضافي')
            print(f"Repetitive words: {repetitive} {'✅ OK' if repetitive <= 3 else '❌ TOO MANY'}")
            
            return repetitive <= 3
            
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("📝 WORD COUNT FIX VERIFICATION")
    print("=" * 40)
    
    # Test Netflix (detailed)
    netflix_ok = test_netflix_word_count()
    
    # Test Google (basic)
    google_ok = test_google_word_count()
    
    # Test Arabic
    arabic_ok = test_arabic_word_count()
    
    print(f"\n🏁 WORD COUNT FIX RESULTS")
    print("=" * 30)
    print(f"Netflix Test: {'✅ PASSED' if netflix_ok else '❌ FAILED'}")
    print(f"Google Test: {'✅ PASSED' if google_ok else '❌ FAILED'}")
    print(f"Arabic Test: {'✅ PASSED' if arabic_ok else '❌ FAILED'}")
    
    if netflix_ok and google_ok and arabic_ok:
        print("\n🎉 WORD COUNT FIX SUCCESS!")
        print("✅ No more repetitive 'additional' words")
        print("✅ Proper word counts maintained")
        print("✅ Natural language extensions")
        print("✅ Both English and Arabic work")
    elif netflix_ok or google_ok:
        print("\n⚠️ PARTIAL SUCCESS")
        print("🔧 Some improvements, but still needs work")
    else:
        print("\n❌ WORD COUNT STILL BROKEN")
        print("🔧 Need to fix the word count function")
