#!/usr/bin/env python
"""
Test script to verify broad categories are working correctly.
"""

import requests
import json

def test_broad_categories():
    """Test that we get broad categories instead of specific service types."""
    print("🏷️ Testing Broad Categories")
    print("=" * 30)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    test_cases = [
        {
            "name": "Netflix",
            "expected_category": "Entertainment",
            "expected_icon": "🎬"
        },
        {
            "name": "Google",
            "expected_category": "Technology", 
            "expected_icon": "💻"
        },
        {
            "name": "Amazon",
            "expected_category": "E-commerce",
            "expected_icon": "🛒"
        },
        {
            "name": "Facebook",
            "expected_category": "Social Media",
            "expected_icon": "📱"
        },
        {
            "name": "Khan Academy",
            "expected_category": "Education",
            "expected_icon": "📚"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Test {i}: {test_case['name']}")
        print("-" * 25)
        
        try:
            response = requests.post(url, json={
                "website_name": test_case['name'],
                "language": "en"
            }, timeout=20)
            
            if response.status_code == 200:
                data = response.json()
                category = data.get('category', {})
                
                category_name = category.get('name', '')
                category_icon = category.get('icon', '')
                
                print(f"✅ Success!")
                print(f"   Category: {category_name} {category_icon}")
                print(f"   Expected: {test_case['expected_category']} {test_case['expected_icon']}")
                
                # Check if we got a broad category (not specific service)
                broad_categories = [
                    'Entertainment', 'Technology', 'E-commerce', 'Social Media', 
                    'Education', 'Finance', 'Healthcare', 'News', 'Gaming'
                ]
                
                is_broad = any(broad in category_name for broad in broad_categories)
                is_not_specific = not any(specific in category_name.lower() for specific in [
                    'streaming service', 'search engine', 'video platform', 
                    'social network', 'online marketplace'
                ])
                
                category_correct = is_broad and is_not_specific
                
                print(f"   Broad Category: {'✅ Yes' if is_broad else '❌ No'}")
                print(f"   Not Specific: {'✅ Yes' if is_not_specific else '❌ No'}")
                print(f"   Overall: {'✅ GOOD' if category_correct else '❌ TOO SPECIFIC'}")
                
                results.append(category_correct)
                
            else:
                print(f"❌ Error: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 BROAD CATEGORIES TEST SUMMARY")
    print("=" * 35)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL CATEGORIES ARE BROAD!")
        print("✅ Getting industry-level categories")
        print("✅ Not getting specific service types")
    elif passed > total // 2:
        print("⚠️ MOSTLY GOOD")
        print("🔧 Some categories still too specific")
    else:
        print("❌ CATEGORIES TOO SPECIFIC")
        print("🔧 Need to improve prompts")
    
    return passed == total

def test_arabic_broad_categories():
    """Test Arabic broad categories."""
    print("\n🌍 Testing Arabic Broad Categories")
    print("=" * 35)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    test_cases = [
        {
            "name": "Netflix",
            "expected_arabic": "الترفيه"
        },
        {
            "name": "Google", 
            "expected_arabic": "التكنولوجيا"
        },
        {
            "name": "Instagram",
            "expected_arabic": "وسائل التواصل الاجتماعي"
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n🔍 Testing {test_case['name']} (Arabic)")
        
        try:
            response = requests.post(url, json={
                "website_name": test_case['name'],
                "language": "ar"
            }, timeout=20)
            
            if response.status_code == 200:
                data = response.json()
                category = data.get('category', {})
                
                category_name = category.get('name', '')
                category_icon = category.get('icon', '')
                
                print(f"✅ Success!")
                print(f"   Category: {category_name} {category_icon}")
                print(f"   Expected: {test_case['expected_arabic']}")
                
                # Check if Arabic and broad
                def has_arabic(text):
                    if not text:
                        return False
                    arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
                    return arabic_chars > len(text) * 0.3
                
                is_arabic = has_arabic(category_name)
                
                broad_arabic_categories = [
                    'الترفيه', 'التكنولوجيا', 'التجارة الإلكترونية', 
                    'وسائل التواصل الاجتماعي', 'التعليم', 'المالية'
                ]
                
                is_broad = any(broad in category_name for broad in broad_arabic_categories)
                
                print(f"   Arabic: {'✅ Yes' if is_arabic else '❌ No'}")
                print(f"   Broad: {'✅ Yes' if is_broad else '❌ No'}")
                
                success = is_arabic and is_broad
                print(f"   Overall: {'✅ GOOD' if success else '❌ NEEDS WORK'}")
                
                results.append(success)
                
            else:
                print(f"❌ Error: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append(False)
    
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Arabic Categories: {passed}/{total} passed")
    
    return passed == total

def quick_category_check():
    """Quick check of a few popular websites."""
    print("\n⚡ Quick Category Check")
    print("=" * 25)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    websites = ["YouTube", "Microsoft", "eBay"]
    
    for website in websites:
        try:
            response = requests.post(url, json={
                "website_name": website,
                "language": "en"
            }, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                category = data.get('category', {})
                
                print(f"{website}: {category.get('name', 'N/A')} {category.get('icon', '')}")
            else:
                print(f"{website}: Error {response.status_code}")
                
        except Exception as e:
            print(f"{website}: Error - {e}")

if __name__ == "__main__":
    print("🏷️ BROAD CATEGORIES TEST SUITE")
    print("=" * 40)
    
    # Quick check first
    quick_category_check()
    
    # Full English test
    english_passed = test_broad_categories()
    
    # Arabic test
    arabic_passed = test_arabic_broad_categories()
    
    print(f"\n🏁 FINAL RESULTS")
    print("=" * 20)
    print(f"English Categories: {'✅ PASSED' if english_passed else '❌ FAILED'}")
    print(f"Arabic Categories: {'✅ PASSED' if arabic_passed else '❌ FAILED'}")
    
    if english_passed and arabic_passed:
        print("\n🎉 BROAD CATEGORIES WORKING!")
        print("✅ Getting Entertainment, Technology, etc.")
        print("✅ Not getting Streaming Service, Search Engine, etc.")
        print("✅ Both English and Arabic work")
    else:
        print("\n⚠️ CATEGORIES NEED ADJUSTMENT")
        print("🔧 Check prompts for broader category guidance")
