#!/usr/bin/env python
"""
Test script to verify website icon functionality.
"""

import requests
import json

def test_website_icons():
    """Test that website icons are included in the response."""
    print("🎨 Testing Website Icons")
    print("=" * 25)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    test_cases = [
        {
            "name": "Netflix",
            "expected_icon_contains": "netflix"
        },
        {
            "name": "Google",
            "expected_icon_contains": "google"
        },
        {
            "name": "YouTube",
            "expected_icon_contains": "youtube"
        },
        {
            "name": "Facebook",
            "expected_icon_contains": "facebook"
        },
        {
            "name": "Amazon",
            "expected_icon_contains": "amazon"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Test {i}: {test_case['name']}")
        print("-" * 25)
        
        try:
            response = requests.post(url, json={
                "website_name": test_case['name'],
                "language": "en"
            }, timeout=20)
            
            if response.status_code == 200:
                data = response.json()
                
                website_icon = data.get('website_icon', '')
                website_name = data.get('name', '')
                category = data.get('category', {})
                
                print(f"✅ Success!")
                print(f"   Name: {website_name}")
                print(f"   Category: {category.get('name', 'N/A')} {category.get('icon', '')}")
                print(f"   Website Icon: {website_icon}")
                
                # Check if icon URL is present and valid
                has_icon = bool(website_icon)
                is_url = website_icon.startswith('http') if website_icon else False
                contains_expected = test_case['expected_icon_contains'].lower() in website_icon.lower() if website_icon else False
                
                print(f"   Has Icon: {'✅ Yes' if has_icon else '❌ No'}")
                print(f"   Valid URL: {'✅ Yes' if is_url else '❌ No'}")
                print(f"   Contains '{test_case['expected_icon_contains']}': {'✅ Yes' if contains_expected else '❌ No'}")
                
                success = has_icon and is_url
                print(f"   Result: {'✅ GOOD' if success else '❌ MISSING'}")
                
                results.append(success)
                
            else:
                print(f"❌ Error: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 WEBSITE ICONS TEST SUMMARY")
    print("=" * 32)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL WEBSITE ICONS WORKING!")
        print("✅ All websites have icon URLs")
        print("✅ Icons are valid HTTP URLs")
        print("✅ Icons match website names")
    elif passed > total // 2:
        print("⚠️ MOSTLY WORKING")
        print("🔧 Some icons missing or invalid")
    else:
        print("❌ WEBSITE ICONS NOT WORKING")
        print("🔧 Most icons are missing")
    
    return passed == total

def test_icon_accessibility():
    """Test if the icon URLs are actually accessible."""
    print("\n🌐 Testing Icon Accessibility")
    print("=" * 30)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    # Test a few popular websites
    websites = ["Netflix", "Google", "YouTube"]
    
    accessible_count = 0
    total_count = 0
    
    for website in websites:
        print(f"\n🔍 Testing {website} icon accessibility...")
        
        try:
            # Get website info
            response = requests.post(url, json={
                "website_name": website,
                "language": "en"
            }, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                icon_url = data.get('website_icon', '')
                
                if icon_url:
                    print(f"   Icon URL: {icon_url}")
                    
                    # Try to access the icon (with a short timeout)
                    try:
                        icon_response = requests.head(icon_url, timeout=5)
                        if icon_response.status_code == 200:
                            print(f"   ✅ Icon accessible (status {icon_response.status_code})")
                            accessible_count += 1
                        else:
                            print(f"   ⚠️ Icon not accessible (status {icon_response.status_code})")
                    except requests.exceptions.RequestException:
                        print(f"   ❌ Icon request failed")
                else:
                    print(f"   ❌ No icon URL provided")
                
                total_count += 1
            else:
                print(f"   ❌ API request failed")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    if total_count > 0:
        accessibility_rate = (accessible_count / total_count) * 100
        print(f"\n📊 Icon Accessibility: {accessible_count}/{total_count} ({accessibility_rate:.1f}%)")
        
        if accessibility_rate >= 80:
            print("✅ Most icons are accessible")
        elif accessibility_rate >= 50:
            print("⚠️ Some icons are accessible")
        else:
            print("❌ Most icons are not accessible")
    
    return accessible_count >= total_count // 2

def test_arabic_website_icons():
    """Test website icons with Arabic language."""
    print("\n🌍 Testing Arabic Website Icons")
    print("=" * 32)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    try:
        response = requests.post(url, json={
            "website_name": "Netflix",
            "language": "ar"
        }, timeout=20)
        
        if response.status_code == 200:
            data = response.json()
            
            website_icon = data.get('website_icon', '')
            category = data.get('category', {})
            
            print(f"✅ Success!")
            print(f"   Name: {data.get('name', 'N/A')}")
            print(f"   Category: {category.get('name', 'N/A')} {category.get('icon', '')}")
            print(f"   Website Icon: {website_icon}")
            
            has_icon = bool(website_icon)
            is_url = website_icon.startswith('http') if website_icon else False
            
            print(f"   Has Icon: {'✅ Yes' if has_icon else '❌ No'}")
            print(f"   Valid URL: {'✅ Yes' if is_url else '❌ No'}")
            
            return has_icon and is_url
            
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def quick_icon_test():
    """Quick test of website icons."""
    print("\n⚡ Quick Icon Test")
    print("=" * 20)
    
    url = "http://localhost:8000/api/books/website-search/"
    
    websites = ["Microsoft", "Apple", "Instagram"]
    
    for website in websites:
        try:
            response = requests.post(url, json={
                "website_name": website,
                "language": "en"
            }, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                icon = data.get('website_icon', '')
                category = data.get('category', {})
                
                icon_status = "✅" if icon and icon.startswith('http') else "❌"
                print(f"{website}: {icon_status} {category.get('name', 'N/A')} {category.get('icon', '')}")
                if icon:
                    print(f"   Icon: {icon[:60]}...")
            else:
                print(f"{website}: ❌ Error {response.status_code}")
                
        except Exception as e:
            print(f"{website}: ❌ Error - {e}")

if __name__ == "__main__":
    print("🎨 WEBSITE ICON TEST SUITE")
    print("=" * 35)
    
    # Quick test first
    quick_icon_test()
    
    # Main icon test
    main_passed = test_website_icons()
    
    # Accessibility test
    accessible = test_icon_accessibility()
    
    # Arabic test
    arabic_passed = test_arabic_website_icons()
    
    print(f"\n🏁 WEBSITE ICON RESULTS")
    print("=" * 25)
    print(f"Icon Presence: {'✅ PASSED' if main_passed else '❌ FAILED'}")
    print(f"Icon Access: {'✅ PASSED' if accessible else '❌ FAILED'}")
    print(f"Arabic Icons: {'✅ PASSED' if arabic_passed else '❌ FAILED'}")
    
    if main_passed and arabic_passed:
        print("\n🎉 WEBSITE ICONS SUCCESS!")
        print("✅ All websites have icon URLs")
        print("✅ Icons work in both languages")
        print("✅ URLs are properly formatted")
        if accessible:
            print("✅ Most icons are accessible")
    else:
        print("\n⚠️ WEBSITE ICONS NEED WORK")
        print("🔧 Check icon URL generation")
