#!/usr/bin/env python
"""
Test script for the new book description analysis API endpoint.
"""

import os
import sys
import django
import json
import requests
import time

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_api_project.settings')
django.setup()

def count_words(text):
    """Count words in a text string."""
    if not text:
        return 0
    return len(text.split())

def is_arabic_text(text):
    """Check if text contains Arabic characters."""
    if not text:
        return False
    arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
    return arabic_chars > len(text) * 0.3

def test_description_analysis():
    """Test the new description analysis API endpoint."""
    print("📖 Book Description Analysis API Test")
    print("=" * 50)
    
    # API endpoint (note the trailing slash)
    url = "http://localhost:8000/api/books/analyze-description/"
    
    # Test cases with different types of book descriptions
    test_cases = [
        {
            "name": "Classic Fiction (English)",
            "data": {
                "description": "A timeless tale of love, pride, and social class in 19th century England. Elizabeth Bennet navigates the complex world of marriage, family expectations, and personal growth while encountering the proud Mr. Darcy. This novel explores themes of prejudice, first impressions, and the importance of looking beyond surface appearances to find true love and understanding.",
                "language": "en"
            },
            "expected_categories": ["Fiction", "Romance", "Classic Literature"]
        },
        {
            "name": "Science Fiction (English)",
            "data": {
                "description": "In a dystopian future where technology controls every aspect of human life, a young programmer discovers a hidden truth about the virtual reality world that everyone inhabits. As artificial intelligence becomes increasingly powerful, humanity must choose between comfortable illusion and harsh reality. This story explores themes of consciousness, free will, and what it means to be human in an age of machines.",
                "language": "en"
            },
            "expected_categories": ["Science Fiction", "Dystopian", "Technology"]
        },
        {
            "name": "Arabic Literature",
            "data": {
                "description": "مجموعة من الحكايات الشعبية العربية التي تحكي قصص الحب والمغامرة والحكمة. تتضمن هذه القصص شخصيات مثل علاء الدين وعلي بابا والأربعين حرامي، وتستكشف موضوعات العدالة والشجاعة والذكاء. هذه الحكايات تعكس الثقافة العربية الغنية وتقدم دروساً أخلاقية قيمة للقراء من جميع الأعمار.",
                "language": "ar"
            },
            "expected_categories": ["أدب شعبي", "حكايات", "تراث عربي"]
        },
        {
            "name": "Historical Biography (English)",
            "data": {
                "description": "The remarkable life story of a pioneering scientist who revolutionized our understanding of the natural world. From humble beginnings to groundbreaking discoveries, this biography chronicles the challenges, failures, and ultimate triumphs of a brilliant mind. The book explores how personal struggles and scientific curiosity combined to produce innovations that changed the course of human history.",
                "language": "en"
            },
            "expected_categories": ["Biography", "History", "Science"]
        },
        {
            "name": "Philosophy (English)",
            "data": {
                "description": "An exploration of fundamental questions about existence, consciousness, and the nature of reality. This philosophical work examines different schools of thought throughout history and presents new perspectives on age-old questions about meaning, purpose, and human nature. The author challenges readers to think critically about their beliefs and assumptions about life and the universe.",
                "language": "en"
            },
            "expected_categories": ["Philosophy", "Metaphysics", "Critical Thinking"]
        }
    ]
    
    total_tests = len(test_cases)
    passed_tests = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📚 Test {i}/{total_tests}: {test_case['name']}")
        print("-" * 40)
        
        start_time = time.time()
        
        try:
            response = requests.post(url, json=test_case['data'], timeout=30)
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"⏱️  Response Time: {response_time:.1f}s")
            
            if response.status_code == 200:
                data = response.json()
                
                print(f"✅ Status: {response.status_code}")
                print(f"📊 Categories Found: {data.get('total_categories', 0)}")
                print(f"🔍 Analysis Summary: {data.get('analysis_summary', 'N/A')[:100]}...")
                
                categories = data.get('categories', [])
                language = test_case['data']['language']
                
                word_count_correct = True
                language_correct = True
                
                # Validate each category
                for j, category in enumerate(categories, 1):
                    name = category.get('name', '')
                    icon = category.get('icon', '')
                    wikilink = category.get('wikilink', '')
                    description = category.get('description', '')
                    
                    print(f"\n   📂 Category {j}:")
                    print(f"      Name: {name}")
                    print(f"      Icon: {icon}")
                    print(f"      Wiki: {wikilink}")
                    
                    # Check word count (should be around 100 words)
                    word_count = count_words(description)
                    word_status = "✅" if 90 <= word_count <= 110 else "❌"
                    print(f"      Description: {word_count} words {word_status}")
                    print(f"      Text: {description[:100]}...")
                    
                    if not (90 <= word_count <= 110):
                        word_count_correct = False
                    
                    # Check language consistency
                    if language == 'ar':
                        name_arabic = is_arabic_text(name)
                        desc_arabic = is_arabic_text(description)
                        if not (name_arabic and desc_arabic):
                            language_correct = False
                            print(f"      ❌ Language issue: Name Arabic: {name_arabic}, Desc Arabic: {desc_arabic}")
                    else:
                        name_english = not is_arabic_text(name)
                        desc_english = not is_arabic_text(description)
                        if not (name_english and desc_english):
                            language_correct = False
                            print(f"      ❌ Language issue: Name English: {name_english}, Desc English: {desc_english}")
                    
                    # Check if wikilink is appropriate for language
                    if language == 'ar' and 'ar.wikipedia.org' not in wikilink:
                        print(f"      ⚠️  Expected Arabic Wikipedia link")
                    elif language == 'en' and 'en.wikipedia.org' not in wikilink:
                        print(f"      ⚠️  Expected English Wikipedia link")
                
                # Overall assessment
                print(f"\n🎯 Assessment:")
                print(f"   Response Time: {'✅ FAST' if response_time < 15 else '❌ SLOW'} ({response_time:.1f}s)")
                print(f"   Word Counts: {'✅ CORRECT' if word_count_correct else '❌ INCORRECT'}")
                print(f"   Language: {'✅ CORRECT' if language_correct else '❌ INCORRECT'}")
                print(f"   Categories: {len(categories)} found")
                
                if response_time < 15 and word_count_correct and language_correct and len(categories) > 0:
                    passed_tests += 1
                    print(f"   Overall: ✅ PASSED")
                else:
                    print(f"   Overall: ❌ FAILED")
                
            else:
                print(f"❌ Error: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"Error details: {error_data}")
                except:
                    print(f"Response: {response.text}")
                
        except requests.exceptions.Timeout:
            print("❌ Request timed out (> 30s)")
        except requests.exceptions.RequestException as e:
            print(f"❌ Request error: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
        
        # Add delay between tests to avoid rate limiting
        if i < total_tests:
            print("⏳ Waiting 5s before next test...")
            time.sleep(5)
    
    # Final summary
    print("\n" + "=" * 50)
    print("📋 DESCRIPTION ANALYSIS TEST SUMMARY")
    print("=" * 50)
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Description analysis API is working perfectly")
        print("📝 Word counts are correct (90-110 words)")
        print("🌍 Language handling is accurate")
        print("⚡ Response times are acceptable")
    elif passed_tests > 0:
        print("⚠️  PARTIAL SUCCESS")
        print("🔧 Some tests passed, API is functional but may need tweaks")
    else:
        print("❌ ALL TESTS FAILED")
        print("🔧 API has significant issues that need fixing")
    
    return passed_tests == total_tests

def test_error_cases():
    """Test error handling for the API."""
    print("\n🚨 Error Handling Test")
    print("=" * 30)
    
    url = "http://localhost:8000/api/books/analyze-description/"
    
    error_cases = [
        {
            "name": "Empty Description",
            "data": {"description": "", "language": "en"},
            "expected_status": 400
        },
        {
            "name": "Short Description",
            "data": {"description": "Too short", "language": "en"},
            "expected_status": 400
        },
        {
            "name": "Invalid Language",
            "data": {"description": "A valid description for testing purposes", "language": "fr"},
            "expected_status": 400
        },
        {
            "name": "Missing Description",
            "data": {"language": "en"},
            "expected_status": 400
        }
    ]
    
    for case in error_cases:
        print(f"\n🧪 Testing: {case['name']}")
        try:
            response = requests.post(url, json=case['data'], timeout=10)
            if response.status_code == case['expected_status']:
                print(f"✅ Correct error handling (status {response.status_code})")
            else:
                print(f"❌ Unexpected status: {response.status_code} (expected {case['expected_status']})")
        except Exception as e:
            print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    print("📖 DESCRIPTION ANALYSIS API TEST SUITE")
    print("=" * 60)
    
    # Test main functionality
    main_passed = test_description_analysis()
    
    # Test error handling
    test_error_cases()
    
    print(f"\n🏁 FINAL RESULT")
    print("=" * 25)
    if main_passed:
        print("✅ DESCRIPTION ANALYSIS API SUCCESS!")
        print("📚 API correctly analyzes book descriptions")
        print("🏷️  Returns structured category information")
        print("📝 Maintains proper word counts (90-110 words)")
        print("🌍 Supports both Arabic and English")
    else:
        print("⚠️  API NEEDS IMPROVEMENT")
        print("🔧 Check the issues identified above")
    
    sys.exit(0 if main_passed else 1)
